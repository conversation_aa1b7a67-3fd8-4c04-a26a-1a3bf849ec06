{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"default\", \"objc-exception\", \"open\", \"regex\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 7668817577240348489, "path": 2762511390010927866, "deps": [[40386456601120721, "percent_encoding", false, 4619155954688014540], [1260461579271933187, "serialize_to_javascript", false, 5830310441466119672], [1441306149310335789, "tempfile", false, 18344929927677668678], [3150220818285335163, "url", false, 12747104869521373637], [3722963349756955755, "once_cell", false, 6634079857458084402], [3988549704697787137, "open", false, 6016604789609726306], [4381063397040571828, "webview2_com", false, 1620107529343145568], [4405182208873388884, "http", false, 8840872042809186653], [4450062412064442726, "dirs_next", false, 17862946225375220352], [4899080583175475170, "semver", false, 10673121565602123838], [5180608563399064494, "tauri_macros", false, 16201455800484990288], [5610773616282026064, "build_script_build", false, 10823161051173062901], [5986029879202738730, "log", false, 13854473596053687594], [7653476968652377684, "windows", false, 2992580185220689273], [8008191657135824715, "thiserror", false, 4180399243737814734], [8292277814562636972, "tauri_utils", false, 10236367114593544923], [8319709847752024821, "uuid", false, 3205901600216734004], [9451456094439810778, "regex", false, 8056837898483478817], [9623796893764309825, "ignore", false, 9412215336901168221], [9689903380558560274, "serde", false, 14133904919686572299], [9920160576179037441, "getrandom", false, 4840162451637317229], [10629569228670356391, "futures_util", false, 12407361663033422850], [11601763207901161556, "tar", false, 6647341680743410605], [11693073011723388840, "raw_window_handle", false, 13820284972097331685], [11989259058781683633, "dunce", false, 15638666645565303671], [12393800526703971956, "tokio", false, 9271451692553479440], [12986574360607194341, "serde_repr", false, 13327017631805533769], [13208667028893622512, "rand", false, 15282804574486708512], [13625485746686963219, "anyhow", false, 6473727346752526497], [14162324460024849578, "tauri_runtime", false, 8873809557699011989], [14564311161534545801, "encoding_rs", false, 10179018853634386755], [15367738274754116744, "serde_json", false, 14144973545746434858], [16228250612241359704, "tauri_runtime_wry", false, 13748349084007558082], [17155886227862585100, "glob", false, 14676570654132184308], [17278893514130263345, "state", false, 3476376675159587216], [17772299992546037086, "flate2", false, 8616107035253531371]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-9d8b00c93021a862\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}