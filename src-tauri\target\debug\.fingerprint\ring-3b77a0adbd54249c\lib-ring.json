{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2241668132362809309, "path": 7558831532039610132, "deps": [[2828590642173593838, "cfg_if", false, 7469923465230619429], [5491919304041016563, "build_script_build", false, 5075506173728258585], [8995469080876806959, "untrusted", false, 270206124238995584], [9920160576179037441, "getrandom", false, 4840162451637317229]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-3b77a0adbd54249c\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}