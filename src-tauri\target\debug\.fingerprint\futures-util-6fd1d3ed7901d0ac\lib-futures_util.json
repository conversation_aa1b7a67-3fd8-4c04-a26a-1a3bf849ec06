{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 15389663814530670383, "deps": [[1615478164327904835, "pin_utils", false, 9088319102551614221], [1906322745568073236, "pin_project_lite", false, 9120953935551481106], [5451793922601807560, "slab", false, 11981280055167465404], [7620660491849607393, "futures_core", false, 9069392543882719828], [10565019901765856648, "futures_macro", false, 2167939452339623277], [16240732885093539806, "futures_task", false, 1675188089003574034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-6fd1d3ed7901d0ac\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}