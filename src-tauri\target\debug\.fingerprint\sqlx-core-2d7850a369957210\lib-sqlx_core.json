{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"_tls-rustls-ring-webpki\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-native-certs\", \"serde\", \"serde_json\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 1384594146405796144, "path": 18301996071108306326, "deps": [[5103565458935487, "futures_io", false, 6323241888413343294], [40386456601120721, "percent_encoding", false, 7157723091141506790], [788558663644978524, "crossbeam_queue", false, 2233159061335169440], [1099106214093768284, "hashbrown", false, 4939240392787528761], [1162433738665300155, "crc", false, 2546127529398922724], [1303438375223863970, "hashlink", false, 7320583058850583928], [3150220818285335163, "url", false, 4097444282919276300], [3646857438214563691, "futures_intrusive", false, 9358458518284646527], [3666196340704888985, "smallvec", false, 13960693664870852389], [3722963349756955755, "once_cell", false, 1128676604584301], [5986029879202738730, "log", false, 1134941415235641757], [6493259146304816786, "indexmap", false, 12832645611579309323], [7620660491849607393, "futures_core", false, 3830813326361008461], [8156804143951879168, "webpki_roots", false, 15605868909699615087], [8319709847752024821, "uuid", false, 1743125873285637349], [8606274917505247608, "tracing", false, 17794938710794229538], [9061476533697426406, "event_listener", false, 9245728909374427490], [9689903380558560274, "serde", false, 16672505391114472160], [9857275760291862238, "sha2", false, 769525638080044329], [9897246384292347999, "chrono", false, 17904666745772013261], [10629569228670356391, "futures_util", false, 13934363597799898367], [10806645703491011684, "thiserror", false, 4015933649290884349], [12170264697963848012, "either", false, 14393582253829827202], [12393800526703971956, "tokio", false, 10007415273039913844], [13077212702700853852, "base64", false, 15659412035968304701], [15367738274754116744, "serde_json", false, 7555008980553358402], [15932120279885307830, "memchr", false, 9973787961473383461], [16066129441945555748, "bytes", false, 11555769161521176802], [16400140949089969347, "rustls", false, 15619569536493161528], [16973251432615581304, "tokio_stream", false, 293291929949008068]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-2d7850a369957210\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}