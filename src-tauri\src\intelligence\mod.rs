use std::collections::HashMap;
use std::sync::Arc;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use fuzzy_matcher::{FuzzyMatcher, skim::SkimMatcherV2};
use crate::database::DatabaseManager;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UrlCompletion {
    pub display: String,
    pub url: String,
    pub confidence: f32,
    pub source: CompletionSource,
    pub visit_count: u32,
    pub last_visited: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CompletionSource {
    History,
    Bookmarks,
    FrequentSites,
    SmartSuggestion,
    SearchEngine,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SmartBookmark {
    pub url: String,
    pub title: String,
    pub visit_count: u32,
    pub time_spent: u64, // seconds
    pub bookmark_score: f32,
    pub suggested_at: i64,
    pub auto_bookmarked: bool,
}

pub struct IntelligenceEngine {
    database: Arc<DatabaseManager>,
    fuzzy_matcher: SkimMatcherV2,
    url_patterns: HashMap<String, String>,
    frequent_sites: Vec<FrequentSite>,
    smart_completions: HashMap<String, Vec<String>>,
}

#[derive(Debug, Clone)]
struct FrequentSite {
    domain: String,
    url: String,
    visit_count: u32,
    average_time_spent: u64,
    last_visited: i64,
}

impl IntelligenceEngine {
    pub async fn new(database: Arc<DatabaseManager>) -> Result<Self> {
        let mut engine = Self {
            database,
            fuzzy_matcher: SkimMatcherV2::default(),
            url_patterns: HashMap::new(),
            frequent_sites: Vec::new(),
            smart_completions: HashMap::new(),
        };

        engine.initialize_patterns().await?;
        engine.load_frequent_sites().await?;
        engine.build_smart_completions().await?;

        Ok(engine)
    }

    async fn initialize_patterns(&mut self) -> Result<()> {
        // Common URL patterns for smart completion
        self.url_patterns.insert("github".to_string(), "https://github.com".to_string());
        self.url_patterns.insert("gmail".to_string(), "https://mail.google.com".to_string());
        self.url_patterns.insert("youtube".to_string(), "https://www.youtube.com".to_string());
        self.url_patterns.insert("twitter".to_string(), "https://twitter.com".to_string());
        self.url_patterns.insert("facebook".to_string(), "https://www.facebook.com".to_string());
        self.url_patterns.insert("linkedin".to_string(), "https://www.linkedin.com".to_string());
        self.url_patterns.insert("reddit".to_string(), "https://www.reddit.com".to_string());
        self.url_patterns.insert("stackoverflow".to_string(), "https://stackoverflow.com".to_string());
        self.url_patterns.insert("docs".to_string(), "https://docs.google.com".to_string());
        self.url_patterns.insert("drive".to_string(), "https://drive.google.com".to_string());
        self.url_patterns.insert("calendar".to_string(), "https://calendar.google.com".to_string());
        self.url_patterns.insert("maps".to_string(), "https://maps.google.com".to_string());
        self.url_patterns.insert("translate".to_string(), "https://translate.google.com".to_string());
        self.url_patterns.insert("news".to_string(), "https://news.google.com".to_string());
        self.url_patterns.insert("amazon".to_string(), "https://www.amazon.com".to_string());
        self.url_patterns.insert("netflix".to_string(), "https://www.netflix.com".to_string());
        self.url_patterns.insert("spotify".to_string(), "https://open.spotify.com".to_string());
        self.url_patterns.insert("discord".to_string(), "https://discord.com".to_string());
        self.url_patterns.insert("slack".to_string(), "https://slack.com".to_string());
        self.url_patterns.insert("zoom".to_string(), "https://zoom.us".to_string());

        Ok(())
    }

    async fn load_frequent_sites(&mut self) -> Result<()> {
        // Load frequently visited sites from database
        let sites = self.database.get_frequent_sites(50).await?;
        self.frequent_sites = sites.into_iter().map(|site| FrequentSite {
            domain: self.extract_domain(&site.url),
            url: site.url,
            visit_count: site.visit_count,
            average_time_spent: site.average_time_spent,
            last_visited: site.last_visited,
        }).collect();

        Ok(())
    }

    async fn build_smart_completions(&mut self) -> Result<()> {
        // Build smart completion mappings based on user behavior
        let history = self.database.get_browsing_history(1000).await?;
        
        for entry in history {
            let domain = self.extract_domain(&entry.url);
            let keywords = self.extract_keywords(&entry.title, &entry.url);
            
            for keyword in keywords {
                self.smart_completions
                    .entry(keyword.to_lowercase())
                    .or_insert_with(Vec::new)
                    .push(entry.url.clone());
            }
        }

        // Deduplicate and sort by frequency
        for completions in self.smart_completions.values_mut() {
            completions.sort();
            completions.dedup();
        }

        Ok(())
    }

    pub async fn get_url_completions(&self, input: &str) -> Result<Vec<UrlCompletion>> {
        let mut completions = Vec::new();
        let input_lower = input.to_lowercase();

        // 1. Direct pattern matches (highest priority)
        if let Some(url) = self.url_patterns.get(&input_lower) {
            completions.push(UrlCompletion {
                display: format!("{} → {}", input, url),
                url: url.clone(),
                confidence: 1.0,
                source: CompletionSource::SmartSuggestion,
                visit_count: 0,
                last_visited: 0,
            });
        }

        // 2. Fuzzy match against patterns
        for (pattern, url) in &self.url_patterns {
            if let Some(score) = self.fuzzy_matcher.fuzzy_match(pattern, &input_lower) {
                if score > 50 { // Threshold for fuzzy matching
                    completions.push(UrlCompletion {
                        display: format!("{} → {}", pattern, url),
                        url: url.clone(),
                        confidence: (score as f32) / 100.0,
                        source: CompletionSource::SmartSuggestion,
                        visit_count: 0,
                        last_visited: 0,
                    });
                }
            }
        }

        // 3. Smart completions from user history
        if let Some(urls) = self.smart_completions.get(&input_lower) {
            for url in urls.iter().take(3) {
                completions.push(UrlCompletion {
                    display: url.clone(),
                    url: url.clone(),
                    confidence: 0.8,
                    source: CompletionSource::History,
                    visit_count: 1, // Would be actual count from database
                    last_visited: 0,
                });
            }
        }

        // 4. Frequent sites fuzzy matching
        for site in &self.frequent_sites {
            if let Some(score) = self.fuzzy_matcher.fuzzy_match(&site.domain, &input_lower) {
                if score > 30 {
                    completions.push(UrlCompletion {
                        display: format!("{} (visited {} times)", site.domain, site.visit_count),
                        url: site.url.clone(),
                        confidence: (score as f32) / 100.0 * 0.7,
                        source: CompletionSource::FrequentSites,
                        visit_count: site.visit_count,
                        last_visited: site.last_visited,
                    });
                }
            }
        }

        // 5. Search engine suggestions
        if input.len() > 2 && !input.contains('.') {
            completions.push(UrlCompletion {
                display: format!("Search for '{}'", input),
                url: format!("https://www.google.com/search?q={}", urlencoding::encode(input)),
                confidence: 0.5,
                source: CompletionSource::SearchEngine,
                visit_count: 0,
                last_visited: 0,
            });
        }

        // Sort by confidence and limit results
        completions.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap());
        completions.truncate(8);

        Ok(completions)
    }

    pub async fn auto_bookmark(&self, url: &str, title: &str, visit_count: u32) -> Result<()> {
        let bookmark_score = self.calculate_bookmark_score(url, title, visit_count).await?;
        
        // Auto-bookmark if score is high enough
        if bookmark_score > 0.7 {
            let smart_bookmark = SmartBookmark {
                url: url.to_string(),
                title: title.to_string(),
                visit_count,
                time_spent: 0, // Would be calculated from actual usage
                bookmark_score,
                suggested_at: chrono::Utc::now().timestamp_millis(),
                auto_bookmarked: true,
            };

            self.database.save_smart_bookmark(&smart_bookmark).await?;
            log::info!("Auto-bookmarked: {} (score: {:.2})", title, bookmark_score);
        }

        Ok(())
    }

    async fn calculate_bookmark_score(&self, url: &str, title: &str, visit_count: u32) -> Result<f32> {
        let mut score = 0.0;

        // Visit frequency (0-0.4)
        score += (visit_count as f32).min(10.0) / 10.0 * 0.4;

        // Domain reputation (0-0.2)
        let domain = self.extract_domain(url);
        if self.is_reputable_domain(&domain) {
            score += 0.2;
        }

        // URL characteristics (0-0.2)
        if url.contains("docs") || url.contains("tutorial") || url.contains("guide") {
            score += 0.1;
        }
        if url.len() > 50 { // Complex URLs often indicate specific resources
            score += 0.1;
        }

        // Title analysis (0-0.2)
        if title.len() > 10 && !title.contains("404") && !title.contains("Error") {
            score += 0.2;
        }

        Ok(score.min(1.0))
    }

    fn is_reputable_domain(&self, domain: &str) -> bool {
        let reputable_domains = [
            "github.com", "stackoverflow.com", "docs.google.com", "developer.mozilla.org",
            "wikipedia.org", "medium.com", "dev.to", "hackernews.com", "reddit.com",
            "youtube.com", "coursera.org", "udemy.com", "edx.org", "mit.edu", "stanford.edu"
        ];
        
        reputable_domains.iter().any(|&d| domain.contains(d))
    }

    fn extract_domain(&self, url: &str) -> String {
        if let Ok(parsed) = url::Url::parse(url) {
            parsed.domain().unwrap_or("").to_string()
        } else {
            "".to_string()
        }
    }

    fn extract_keywords(&self, title: &str, url: &str) -> Vec<String> {
        let mut keywords = Vec::new();
        
        // Extract from title
        for word in title.split_whitespace() {
            if word.len() > 3 {
                keywords.push(word.to_lowercase());
            }
        }

        // Extract from URL path
        if let Ok(parsed) = url::Url::parse(url) {
            for segment in parsed.path_segments().unwrap_or_default() {
                if segment.len() > 3 && !segment.chars().all(|c| c.is_numeric()) {
                    keywords.push(segment.to_lowercase());
                }
            }
        }

        keywords
    }

    pub async fn preload_frequent_sites(&self) -> Result<()> {
        // Preload DNS and potentially prefetch resources for frequent sites
        for site in &self.frequent_sites {
            if site.visit_count > 5 {
                // TODO: Implement DNS preloading and resource prefetching
                log::info!("Preloading: {}", site.domain);
            }
        }
        Ok(())
    }

    pub async fn update_site_usage(&mut self, url: &str, time_spent: u64) -> Result<()> {
        // Update usage statistics for intelligent suggestions
        self.database.update_site_usage(url, time_spent).await?;
        
        // Refresh frequent sites if needed
        if self.frequent_sites.len() < 50 {
            self.load_frequent_sites().await?;
        }

        Ok(())
    }

    pub async fn get_bookmark_suggestions(&self) -> Result<Vec<SmartBookmark>> {
        self.database.get_bookmark_suggestions().await
    }
}
