{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2225463790103693989, "path": 7558831532039610132, "deps": [[2828590642173593838, "cfg_if", false, 8324370001129048503], [5491919304041016563, "build_script_build", false, 5075506173728258585], [8995469080876806959, "untrusted", false, 10405792476215655174], [9920160576179037441, "getrandom", false, 13761139153277698645]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-919aec944444b2cc\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}