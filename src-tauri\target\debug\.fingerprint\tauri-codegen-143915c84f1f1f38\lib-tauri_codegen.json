{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 7493274143156239165, "deps": [[3060637413840920116, "proc_macro2", false, 14143828410907889523], [3150220818285335163, "url", false, 4097444282919276300], [4899080583175475170, "semver", false, 10410564831146079169], [4974441333307933176, "syn", false, 4149661804382479018], [7170110829644101142, "json_patch", false, 15020196521978777085], [7392050791754369441, "ico", false, 7629597027086246491], [8319709847752024821, "uuid", false, 1743125873285637349], [9689903380558560274, "serde", false, 16672505391114472160], [9857275760291862238, "sha2", false, 769525638080044329], [10806645703491011684, "thiserror", false, 4015933649290884349], [11050281405049894993, "tauri_utils", false, 17386037368210056767], [12687914511023397207, "png", false, 15722801764774915128], [13077212702700853852, "base64", false, 15659412035968304701], [14132538657330703225, "brotli", false, 9327308859271599338], [15367738274754116744, "serde_json", false, 7555008980553358402], [15622660310229662834, "walkdir", false, 15099281372303424341], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-143915c84f1f1f38\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}