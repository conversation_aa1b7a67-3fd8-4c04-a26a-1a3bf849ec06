{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 14759153068986661120, "deps": [[561782849581144631, "html5ever", false, 18303533528607310979], [1200537532907108615, "url<PERSON><PERSON>n", false, 7760015927404048324], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [3150220818285335163, "url", false, 4097444282919276300], [3191507132440681679, "serde_untagged", false, 12274998486722907519], [4071963112282141418, "serde_with", false, 14935190061920020863], [4899080583175475170, "semver", false, 10410564831146079169], [5986029879202738730, "log", false, 1134941415235641757], [6262254372177975231, "kuchiki", false, 10899291759454624775], [6606131838865521726, "ctor", false, 5074607611391229500], [6913375703034175521, "schemars", false, 14149306323999585006], [7170110829644101142, "json_patch", false, 15020196521978777085], [8319709847752024821, "uuid", false, 1743125873285637349], [9010263965687315507, "http", false, 11985562838604801511], [9090328626728818999, "toml", false, 16959393990023363662], [9451456094439810778, "regex", false, 2655336738413197755], [9689903380558560274, "serde", false, 16672505391114472160], [10806645703491011684, "thiserror", false, 4015933649290884349], [11655476559277113544, "cargo_metadata", false, 2882631228274941143], [11989259058781683633, "dunce", false, 18370474174498096977], [13625485746686963219, "anyhow", false, 9618011853274328708], [14132538657330703225, "brotli", false, 9327308859271599338], [15367738274754116744, "serde_json", false, 7555008980553358402], [15622660310229662834, "walkdir", false, 15099281372303424341], [15932120279885307830, "memchr", false, 9973787961473383461], [17146114186171651583, "infer", false, 8178060588107728012], [17155886227862585100, "glob", false, 11939270752987892944], [17186037756130803222, "phf", false, 7724087699442270889], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-ae12e508fc699e4d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}