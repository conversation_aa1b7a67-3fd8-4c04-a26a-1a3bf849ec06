{"$message_type":"diagnostic","message":"couldn't read `src\\memory\\mod.rs`: stream did not contain valid UTF-8","code":null,"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":313,"byte_end":324,"line_start":11,"line_end":11,"column_start":1,"column_end":12,"is_primary":true,"text":[{"text":"mod memory;","highlight_start":1,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"byte `255` is not valid utf-8","code":null,"level":"note","spans":[{"file_name":"src\\memory\\mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"��u\u0000s\u0000e\u0000 \u0000a\u0000n\u0000y\u0000h\u0000o\u0000w\u0000:\u0000:\u0000R\u0000e\u0000s\u0000u\u0000l\u0000t\u0000;\u0000 \u0000p\u0000u\u0000b\u0000 \u0000s\u0000t\u0000r\u0000u\u0000c\u0000t\u0000 \u0000M\u0000e\u0000m\u0000o\u0000r\u0000y\u0000M\u0000a\u0000n\u0000a\u0000g\u0000e\u0000r\u0000;\u0000 \u0000i\u0000m\u0000p\u0000l\u0000 \u0000M\u0000e\u0000m\u0000o\u0000r\u0000y\u0000M\u0000a\u0000n\u0000a\u0000g\u0000e\u0000r\u0000 \u0000{\u0000 \u0000p\u0000u\u0000b\u0000 \u0000a\u0000s\u0000y\u0000n\u0000c\u0000 \u0000f\u0000n\u0000 \u0000n\u0000e\u0000w\u0000(\u0000_\u0000d\u0000b\u0000:\u0000 \u0000s\u0000t\u0000d\u0000:\u0000:\u0000s\u0000y\u0000n\u0000c\u0000:\u0000:\u0000A\u0000r\u0000c\u0000<\u0000c\u0000r\u0000a\u0000t\u0000e\u0000:\u0000:\u0000d\u0000a\u0000t\u0000a\u0000b\u0000a\u0000s\u0000e\u0000:\u0000:\u0000D\u0000a\u0000t\u0000a\u0000b\u0000a\u0000s\u0000e\u0000M\u0000a\u0000n\u0000a\u0000g\u0000e\u0000r\u0000>\u0000)\u0000 \u0000-\u0000>\u0000 \u0000R\u0000e\u0000s\u0000u\u0000l\u0000t\u0000<\u0000S\u0000e\u0000l\u0000f\u0000>\u0000 \u0000{\u0000 \u0000O\u0000k\u0000(\u0000S\u0000e\u0000l\u0000f\u0000)\u0000 \u0000}\u0000 \u0000}\u0000\r\u0000","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: couldn't read `src\\memory\\mod.rs`: stream did not contain valid UTF-8\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:11:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mmod memory;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: byte `255` is not valid utf-8\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\memory\\mod.rs:1:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m��u␀s␀e␀ ␀a␀n␀y␀h␀o␀w␀:␀:␀R␀e␀s␀u␀l␀t␀;␀ ␀p␀u␀b␀ ␀s␀t␀r␀u␀c␀t␀ ␀M␀e␀m␀o␀r␀y␀M␀a␀n␀a␀g␀e␀r␀;␀ ␀i␀m␀p␀l␀ ␀M␀e␀m␀o␀\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
