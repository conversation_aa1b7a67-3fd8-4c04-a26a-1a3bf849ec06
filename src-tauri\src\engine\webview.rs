use std::collections::HashMap;
use std::sync::Arc;
use anyhow::Result;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tauri::{WebviewWindow, WebviewUrl, Manager};
use wry::WebViewBuilder;
use tracing::{info, warn, error};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebviewConfig {
    pub enable_devtools: bool,
    pub enable_context_menu: bool,
    pub enable_file_drop: bool,
    pub user_agent: String,
    pub zoom_level: f64,
    pub enable_smooth_scrolling: bool,
    pub enable_hardware_acceleration: bool,
    pub memory_limit_mb: u64,
}

impl Default for WebviewConfig {
    fn default() -> Self {
        Self {
            enable_devtools: cfg!(debug_assertions),
            enable_context_menu: true,
            enable_file_drop: true,
            user_agent: "Memori/1.0 (AI-Enhanced Browser)".to_string(),
            zoom_level: 1.0,
            enable_smooth_scrolling: true,
            enable_hardware_acceleration: true,
            memory_limit_mb: 512,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebviewState {
    pub tab_id: String,
    pub window_label: String,
    pub current_url: String,
    pub title: String,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
    pub zoom_level: f64,
    pub is_muted: bool,
    pub security_state: SecurityState,
    pub performance_metrics: WebviewPerformanceMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityState {
    Secure,
    Insecure,
    Mixed,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebviewPerformanceMetrics {
    pub load_time_ms: u64,
    pub dom_content_loaded_ms: u64,
    pub first_paint_ms: u64,
    pub largest_contentful_paint_ms: u64,
    pub cumulative_layout_shift: f64,
    pub memory_usage_mb: f64,
    pub js_heap_size_mb: f64,
}

impl Default for WebviewPerformanceMetrics {
    fn default() -> Self {
        Self {
            load_time_ms: 0,
            dom_content_loaded_ms: 0,
            first_paint_ms: 0,
            largest_contentful_paint_ms: 0,
            cumulative_layout_shift: 0.0,
            memory_usage_mb: 0.0,
            js_heap_size_mb: 0.0,
        }
    }
}

pub struct WebviewManager {
    webviews: Arc<RwLock<HashMap<String, WebviewState>>>,
    config: WebviewConfig,
}

impl WebviewManager {
    pub fn new(config: WebviewConfig) -> Self {
        Self {
            webviews: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    pub async fn create_webview(
        &self,
        app_handle: &tauri::AppHandle,
        tab_id: &str,
        url: &str,
        title: &str,
    ) -> Result<WebviewState> {
        let window_label = format!("webview-{}", tab_id);
        
        // Create webview window with optimized settings
        let webview_window = tauri::WebviewWindowBuilder::new(
            app_handle,
            &window_label,
            WebviewUrl::External(url.parse()?)
        )
        .title(title)
        .inner_size(1200.0, 800.0)
        .min_inner_size(400.0, 300.0)
        .resizable(true)
        .maximizable(true)
        .minimizable(true)
        .closable(true)
        .focused(false)
        .visible(false) // Start hidden for faster perceived loading
        .user_agent(&self.config.user_agent)
        .initialization_script(&self.get_initialization_script(tab_id))
        .build()?;

        // Configure webview for performance
        self.configure_webview_performance(&webview_window).await?;

        let state = WebviewState {
            tab_id: tab_id.to_string(),
            window_label: window_label.clone(),
            current_url: url.to_string(),
            title: title.to_string(),
            is_loading: true,
            can_go_back: false,
            can_go_forward: false,
            zoom_level: self.config.zoom_level,
            is_muted: false,
            security_state: SecurityState::Unknown,
            performance_metrics: WebviewPerformanceMetrics::default(),
        };

        // Store webview state
        self.webviews.write().insert(tab_id.to_string(), state.clone());

        // Set up event handlers
        self.setup_webview_handlers(&webview_window, tab_id).await?;

        info!("Created webview for tab: {} with URL: {}", tab_id, url);
        Ok(state)
    }

    pub async fn navigate(&self, tab_id: &str, url: &str) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.current_url = url.to_string();
            state.is_loading = true;
            state.security_state = SecurityState::Unknown;
            
            // Reset performance metrics
            state.performance_metrics = WebviewPerformanceMetrics::default();
            
            info!("Navigating tab {} to: {}", tab_id, url);
        }
        Ok(())
    }

    pub async fn update_title(&self, tab_id: &str, title: &str) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.title = title.to_string();
        }
        Ok(())
    }

    pub async fn update_loading_state(&self, tab_id: &str, is_loading: bool) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.is_loading = is_loading;
        }
        Ok(())
    }

    pub async fn update_navigation_state(&self, tab_id: &str, can_go_back: bool, can_go_forward: bool) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.can_go_back = can_go_back;
            state.can_go_forward = can_go_forward;
        }
        Ok(())
    }

    pub async fn update_security_state(&self, tab_id: &str, security_state: SecurityState) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.security_state = security_state;
        }
        Ok(())
    }

    pub async fn update_performance_metrics(&self, tab_id: &str, metrics: WebviewPerformanceMetrics) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.performance_metrics = metrics;
        }
        Ok(())
    }

    pub async fn set_zoom_level(&self, tab_id: &str, zoom_level: f64) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.zoom_level = zoom_level;
        }
        Ok(())
    }

    pub async fn set_muted(&self, tab_id: &str, is_muted: bool) -> Result<()> {
        if let Some(mut state) = self.webviews.write().get_mut(tab_id) {
            state.is_muted = is_muted;
        }
        Ok(())
    }

    pub fn get_webview_state(&self, tab_id: &str) -> Option<WebviewState> {
        self.webviews.read().get(tab_id).cloned()
    }

    pub fn get_all_webview_states(&self) -> Vec<WebviewState> {
        self.webviews.read().values().cloned().collect()
    }

    pub async fn remove_webview(&self, tab_id: &str) -> Option<WebviewState> {
        self.webviews.write().remove(tab_id)
    }

    async fn configure_webview_performance(&self, window: &WebviewWindow) -> Result<()> {
        // Inject performance optimizations
        let performance_script = r#"
            // Optimize rendering performance
            if ('requestIdleCallback' in window) {
                requestIdleCallback(() => {
                    // Defer non-critical operations
                    console.log('Memori: Optimizing performance');
                });
            }
            
            // Memory management
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'memory') {
                            window.__TAURI__.invoke('update_memory_usage', {
                                tabId: window.__MEMORI_TAB_ID__,
                                memoryUsage: entry.usedJSHeapSize / 1024 / 1024
                            });
                        }
                    }
                });
                
                try {
                    observer.observe({ entryTypes: ['memory', 'navigation', 'paint'] });
                } catch (e) {
                    console.warn('Performance observer not fully supported');
                }
            }
        "#;

        window.webview().evaluate_script(performance_script)?;
        Ok(())
    }

    fn get_initialization_script(&self, tab_id: &str) -> String {
        format!(r#"
            // Memori Browser Webview Initialization
            (function() {{
                'use strict';
                
                // Set tab ID for communication
                window.__MEMORI_TAB_ID__ = '{}';
                
                // Performance monitoring
                let performanceData = {{}};
                
                // Navigation timing
                window.addEventListener('load', () => {{
                    if (performance.timing) {{
                        const timing = performance.timing;
                        performanceData = {{
                            tabId: window.__MEMORI_TAB_ID__,
                            loadTime: timing.loadEventEnd - timing.navigationStart,
                            domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                            firstPaint: 0,
                            largestContentfulPaint: 0
                        }};
                        
                        // Get paint metrics
                        if ('getEntriesByType' in performance) {{
                            const paintEntries = performance.getEntriesByType('paint');
                            for (const entry of paintEntries) {{
                                if (entry.name === 'first-paint') {{
                                    performanceData.firstPaint = entry.startTime;
                                }} else if (entry.name === 'first-contentful-paint') {{
                                    performanceData.firstContentfulPaint = entry.startTime;
                                }}
                            }}
                        }}
                        
                        // Send performance data
                        window.__TAURI__.invoke('update_performance_metrics', performanceData);
                    }}
                }});
                
                // Security state detection
                function updateSecurityState() {{
                    let securityState = 'unknown';
                    
                    if (location.protocol === 'https:') {{
                        securityState = 'secure';
                    }} else if (location.protocol === 'http:') {{
                        securityState = 'insecure';
                    }}
                    
                    // Check for mixed content
                    const mixedContent = document.querySelectorAll('img[src^="http:"], script[src^="http:"], link[href^="http:"]');
                    if (mixedContent.length > 0 && location.protocol === 'https:') {{
                        securityState = 'mixed';
                    }}
                    
                    window.__TAURI__.invoke('update_security_state', {{
                        tabId: window.__MEMORI_TAB_ID__,
                        securityState
                    }});
                }}
                
                // Audio detection
                let audioContext = null;
                let isPlayingAudio = false;
                
                function detectAudio() {{
                    const mediaElements = document.querySelectorAll('audio, video');
                    let hasAudio = false;
                    
                    for (const element of mediaElements) {{
                        if (!element.paused && !element.muted && element.currentTime > 0) {{
                            hasAudio = true;
                            break;
                        }}
                    }}
                    
                    if (hasAudio !== isPlayingAudio) {{
                        isPlayingAudio = hasAudio;
                        window.__TAURI__.invoke('update_audio_state', {{
                            tabId: window.__MEMORI_TAB_ID__,
                            isPlaying: hasAudio
                        }});
                    }}
                }}
                
                // Monitor audio every 500ms
                setInterval(detectAudio, 500);
                
                // Title changes
                const titleObserver = new MutationObserver(() => {{
                    window.__TAURI__.invoke('update_title', {{
                        tabId: window.__MEMORI_TAB_ID__,
                        title: document.title
                    }});
                }});
                
                if (document.querySelector('title')) {{
                    titleObserver.observe(document.querySelector('title'), {{
                        childList: true,
                        subtree: true
                    }});
                }}
                
                // Page load events
                window.addEventListener('load', () => {{
                    updateSecurityState();
                    window.__TAURI__.invoke('update_loading_state', {{
                        tabId: window.__MEMORI_TAB_ID__,
                        isLoading: false
                    }});
                }});
                
                window.addEventListener('beforeunload', () => {{
                    window.__TAURI__.invoke('update_loading_state', {{
                        tabId: window.__MEMORI_TAB_ID__,
                        isLoading: true
                    }});
                }});
                
                // Navigation state
                window.addEventListener('popstate', () => {{
                    window.__TAURI__.invoke('update_navigation_state', {{
                        tabId: window.__MEMORI_TAB_ID__,
                        canGoBack: window.history.length > 1,
                        canGoForward: false // Browser API limitation
                    }});
                }});
                
                console.log('Memori webview initialized for tab:', window.__MEMORI_TAB_ID__);
            }})();
        "#, tab_id)
    }

    async fn setup_webview_handlers(&self, window: &WebviewWindow, tab_id: &str) -> Result<()> {
        // Additional webview-specific setup can go here
        info!("Set up handlers for webview: {}", tab_id);
        Ok(())
    }
}
