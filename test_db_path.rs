use std::path::PathBuf;

fn main() {
    let mut path = dirs::data_dir()
        .expect("Could not find data directory");
    
    path.push("memori-browser");
    path.push("browser.db");
    
    println!("Database path: {}", path.display());
    println!("Parent directory: {:?}", path.parent());
    
    // Check if parent exists
    if let Some(parent) = path.parent() {
        println!("Parent exists: {}", parent.exists());
    }
}
