cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=ext/vswhom.cpp
OPT_LEVEL = Some(0)
OUT_DIR = Some(C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\vswhom-sys-ccdbdb78441c71e6\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\memori-browser\src-tauri\target\debug\deps;C:\Users\<USER>\memori-browser\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\;C:\ProgramData\chocolatey\bin;C:\ActiveTcl\lib\tcl8.6;C:\ActiveTcl\lib\tk8.6;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\src\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm 2024.2.1\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\src\flutter\flutter\flutter\bin;C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools;C:\Program Files\nodejs\;C:\Program Files\Kotlin\kotlin\bin;;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git LFS;C:\Program Files\Void\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm 2024.3.5\bin;C:\Program Files\PostgreSQL\16\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\src\flutter\bin;C:\src\flutter\flutter\flutter\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Gradle\gradle-8.5\bin;C:\Program Files\Kotlin\kotlin\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2025.1.1.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.pyenv\bin;C:\Users\<USER>\.pyenv\shims;C:\Users\<USER>\.pyenv\bin;C:\Users\<USER>\.pyenv\shims;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
vswhom.cpp
ext/vswhom.cpp(213): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(216): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(431): warning C4456: declaration of 'hr' hides previous local declaration
ext/vswhom.cpp(418): note: see declaration of 'hr'
ext/vswhom.cpp(459): warning C4244: 'argument': conversion from 'LONGLONG' to 'int', possible loss of data
ext/vswhom.cpp(502): warning C4456: declaration of 'rc' hides previous local declaration
ext/vswhom.cpp(410): note: see declaration of 'rc'
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.41.34120\atlmfc\lib\x64
cargo:rustc-link-lib=static=vswhom
cargo:rustc-link-search=native=C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\vswhom-sys-ccdbdb78441c71e6\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-lib=dylib=OleAut32
cargo:rustc-link-lib=dylib=Ole32
