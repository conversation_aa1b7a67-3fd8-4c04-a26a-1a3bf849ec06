{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9555057514056860554, "build_script_build", false, 1977755734966177455]], "local": [{"RerunIfChanged": {"output": "debug\\build\\memori-browser-f0d0c69889593e0f\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}