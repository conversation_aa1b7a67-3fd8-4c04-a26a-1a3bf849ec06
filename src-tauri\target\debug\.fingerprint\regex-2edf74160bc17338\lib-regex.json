{"rustc": 1842507548689473721, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 4999240884463334084, "deps": [[555019317135488525, "regex_automata", false, 7493950824076121246], [2779309023524819297, "aho_corasick", false, 7234542628749937595], [9408802513701742484, "regex_syntax", false, 17630277904068216774], [15932120279885307830, "memchr", false, 3149709465132557017]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-2edf74160bc17338\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}