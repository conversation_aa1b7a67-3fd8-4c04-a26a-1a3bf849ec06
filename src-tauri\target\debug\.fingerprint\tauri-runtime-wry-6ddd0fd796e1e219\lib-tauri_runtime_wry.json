{"rustc": 1842507548689473721, "features": "[\"arboard\", \"clipboard\", \"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 10956804577811572050, "deps": [[4381063397040571828, "webview2_com", false, 9628900648478487160], [7653476968652377684, "windows", false, 3715085020098981444], [8292277814562636972, "tauri_utils", false, 1597570392376008931], [8319709847752024821, "uuid", false, 12411518605472132979], [8391357152270261188, "wry", false, 17435219131856974349], [11693073011723388840, "raw_window_handle", false, 2214521491364938754], [13208667028893622512, "rand", false, 14866442077337734985], [14162324460024849578, "tauri_runtime", false, 13982910155087855879], [16228250612241359704, "build_script_build", false, 3958620358155391614], [17455546449572272057, "arboard", false, 10227268650363682958]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-6ddd0fd796e1e219\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}