{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 21500578101221346, "deps": [[4450062412064442726, "dirs_next", false, 14643780018954267349], [4899080583175475170, "semver", false, 11305946926429843468], [7468248713591957673, "cargo_toml", false, 13052768063281998370], [8292277814562636972, "tauri_utils", false, 6374643587899887886], [9689903380558560274, "serde", false, 16672505391114472160], [10301936376833819828, "json_patch", false, 2606004407255906207], [13077543566650298139, "heck", false, 11003689655865491247], [13625485746686963219, "anyhow", false, 9618011853274328708], [14189313126492979171, "tauri_winres", false, 7097099183083787360], [15367738274754116744, "serde_json", false, 7155776428369128707], [15622660310229662834, "walkdir", false, 15099281372303424341]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-161c25428b332615\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}