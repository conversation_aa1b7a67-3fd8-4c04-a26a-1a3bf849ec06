{"rustc": 1842507548689473721, "features": "[\"default\", \"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2241668132362809309, "path": 14528530421717216679, "deps": [[3007252114546291461, "tao", false, 10870830032628926996], [3150220818285335163, "url", false, 18341895287846504686], [3540822385484940109, "windows_implement", false, 10822027573035950352], [3722963349756955755, "once_cell", false, 6634079857458084402], [4381063397040571828, "webview2_com", false, 15263397648579214487], [4405182208873388884, "http", false, 10234720921281853277], [4684437522915235464, "libc", false, 15548930538968033071], [5986029879202738730, "log", false, 8783125871897346445], [7653476968652377684, "windows", false, 2992580185220689273], [8008191657135824715, "thiserror", false, 4180399243737814734], [8391357152270261188, "build_script_build", false, 5449838677121257818], [9689903380558560274, "serde", false, 3408844655873488430], [11989259058781683633, "dunce", false, 15638666645565303671], [15367738274754116744, "serde_json", false, 1374970658716955935]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-910bef34db9e5265\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}