{"rustc": 1842507548689473721, "features": "[\"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 10956804577811572050, "deps": [[4381063397040571828, "webview2_com", false, 15263397648579214487], [7653476968652377684, "windows", false, 2992580185220689273], [8292277814562636972, "tauri_utils", false, 11233730906379427994], [8319709847752024821, "uuid", false, 1289308709912327355], [8391357152270261188, "wry", false, 3414771229419470247], [11693073011723388840, "raw_window_handle", false, 13820284972097331685], [13208667028893622512, "rand", false, 15282804574486708512], [14162324460024849578, "tauri_runtime", false, 13599882102836093009], [16228250612241359704, "build_script_build", false, 749614940002239050]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-2011ff95080a00ea\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}