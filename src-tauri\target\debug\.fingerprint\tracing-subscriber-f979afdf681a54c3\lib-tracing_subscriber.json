{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 6157695917084337112, "deps": [[1009387600818341822, "matchers", false, 12043978575735608561], [1017461770342116999, "sharded_slab", false, 17953645938090810558], [1359731229228270592, "thread_local", false, 10374109062860704895], [3424551429995674438, "tracing_core", false, 10544368577322630802], [3666196340704888985, "smallvec", false, 5434617118796683908], [3722963349756955755, "once_cell", false, 6634079857458084402], [6981130804689348050, "tracing_serde", false, 2644835788305265483], [8606274917505247608, "tracing", false, 5299873891725318183], [8614575489689151157, "nu_ansi_term", false, 13069895489809237651], [9451456094439810778, "regex", false, 13118743738096404882], [9689903380558560274, "serde", false, 3408844655873488430], [10806489435541507125, "tracing_log", false, 16469770491541741968], [15367738274754116744, "serde_json", false, 12446843484317769701]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-f979afdf681a54c3\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}