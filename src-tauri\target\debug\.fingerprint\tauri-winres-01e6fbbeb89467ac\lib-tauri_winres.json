{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10555948925697328552, "profile": 2225463790103693989, "path": 6102255561526257095, "deps": [[1293861355733423614, "toml", false, 16119890746375262653], [11501392865286155686, "embed_resource", false, 13916855295193178528]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-01e6fbbeb89467ac\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}