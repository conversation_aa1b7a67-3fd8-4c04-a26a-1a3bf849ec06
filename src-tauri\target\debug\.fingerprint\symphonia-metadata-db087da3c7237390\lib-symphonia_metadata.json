{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2684992013079232362, "profile": 2241668132362809309, "path": 6100933823666395521, "deps": [[1218881066841546592, "symphonia_core", false, 14242599090913191530], [5986029879202738730, "log", false, 8783125871897346445], [14564311161534545801, "encoding_rs", false, 10179018853634386755], [17917672826516349275, "lazy_static", false, 2390581858057747413]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-metadata-db087da3c7237390\\dep-lib-symphonia_metadata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}