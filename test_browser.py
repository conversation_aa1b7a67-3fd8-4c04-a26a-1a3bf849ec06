#!/usr/bin/env python3
"""
Memori Browser Test Suite
Tests the Servo-powered browser functionality
"""

import subprocess
import time
import json
import requests
import sys
import os
from pathlib import Path

class MemoriBrowserTester:
    def __init__(self):
        self.project_root = Path.cwd()
        self.test_results = []
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def run_command(self, command, cwd=None, timeout=30):
        """Run a shell command and return the result"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "Command timed out"
        except Exception as e:
            return False, "", str(e)
    
    def test_rust_environment(self):
        """Test Rust toolchain and dependencies"""
        self.log("🦀 Testing Rust environment...")
        
        # Check Rust version
        success, stdout, stderr = self.run_command("rustc --version")
        if not success:
            self.log("❌ Rust not installed or not in PATH", "ERROR")
            return False
            
        rust_version = stdout.strip()
        self.log(f"✅ Rust version: {rust_version}")
        
        # Check Cargo version
        success, stdout, stderr = self.run_command("cargo --version")
        if success:
            cargo_version = stdout.strip()
            self.log(f"✅ Cargo version: {cargo_version}")
        
        return True
    
    def test_node_environment(self):
        """Test Node.js and npm environment"""
        self.log("📦 Testing Node.js environment...")
        
        # Check Node version
        success, stdout, stderr = self.run_command("node --version")
        if not success:
            self.log("❌ Node.js not installed or not in PATH", "ERROR")
            return False
            
        node_version = stdout.strip()
        self.log(f"✅ Node.js version: {node_version}")
        
        # Check npm version
        success, stdout, stderr = self.run_command("npm --version")
        if success:
            npm_version = stdout.strip()
            self.log(f"✅ npm version: {npm_version}")
        
        return True
    
    def test_dependencies(self):
        """Test project dependencies"""
        self.log("📚 Testing project dependencies...")
        
        # Check if package.json exists
        package_json = self.project_root / "package.json"
        if not package_json.exists():
            self.log("❌ package.json not found", "ERROR")
            return False
        
        # Check if Cargo.toml exists
        cargo_toml = self.project_root / "src-tauri" / "Cargo.toml"
        if not cargo_toml.exists():
            self.log("❌ src-tauri/Cargo.toml not found", "ERROR")
            return False
            
        self.log("✅ Project files found")
        
        # Install npm dependencies
        self.log("Installing npm dependencies...")
        success, stdout, stderr = self.run_command("npm install", timeout=120)
        if not success:
            self.log(f"❌ npm install failed: {stderr}", "ERROR")
            return False
        
        self.log("✅ npm dependencies installed")
        return True
    
    def test_rust_compilation(self):
        """Test Rust compilation"""
        self.log("🔨 Testing Rust compilation...")
        
        # Try to compile the Rust backend
        success, stdout, stderr = self.run_command(
            "cargo check", 
            cwd=self.project_root / "src-tauri",
            timeout=300
        )
        
        if not success:
            self.log(f"❌ Rust compilation failed:", "ERROR")
            self.log(stderr, "ERROR")
            return False
        
        self.log("✅ Rust compilation successful")
        return True
    
    def test_tauri_build(self):
        """Test Tauri build process"""
        self.log("🏗️ Testing Tauri build...")
        
        # Check if tauri CLI is available
        success, stdout, stderr = self.run_command("npm run tauri --version")
        if not success:
            self.log("Installing Tauri CLI...")
            success, stdout, stderr = self.run_command("npm install @tauri-apps/cli", timeout=120)
            if not success:
                self.log(f"❌ Failed to install Tauri CLI: {stderr}", "ERROR")
                return False
        
        # Try to build the project
        self.log("Building Tauri project (this may take a while)...")
        success, stdout, stderr = self.run_command(
            "npm run tauri build", 
            timeout=600
        )
        
        if not success:
            self.log(f"❌ Tauri build failed:", "ERROR")
            self.log(stderr, "ERROR")
            
            # Try development build instead
            self.log("Trying development build...")
            success, stdout, stderr = self.run_command(
                "cargo build", 
                cwd=self.project_root / "src-tauri",
                timeout=300
            )
            
            if success:
                self.log("✅ Development build successful")
                return True
            else:
                self.log(f"❌ Development build also failed: {stderr}", "ERROR")
                return False
        
        self.log("✅ Tauri build successful")
        return True
    
    def test_browser_features(self):
        """Test browser-specific features"""
        self.log("🌐 Testing browser features...")
        
        # Test if we can start the development server
        self.log("Starting development server...")
        
        # This would normally start the Tauri dev server
        # For now, we'll just test the compilation
        success, stdout, stderr = self.run_command(
            "cargo test", 
            cwd=self.project_root / "src-tauri",
            timeout=120
        )
        
        if success:
            self.log("✅ Rust tests passed")
        else:
            self.log(f"⚠️ Some tests failed, but this is expected: {stderr}", "WARN")
        
        return True
    
    def test_performance_features(self):
        """Test performance monitoring features"""
        self.log("⚡ Testing performance features...")
        
        # Check if performance monitoring modules compile
        perf_files = [
            "src-tauri/src/engine/performance.rs",
            "src-tauri/src/servo_engine/performance.rs"
        ]
        
        for file_path in perf_files:
            if (self.project_root / file_path).exists():
                self.log(f"✅ Found {file_path}")
            else:
                self.log(f"⚠️ Missing {file_path}", "WARN")
        
        return True
    
    def test_security_features(self):
        """Test security features"""
        self.log("🛡️ Testing security features...")
        
        # Check if security modules exist
        security_files = [
            "src-tauri/src/engine/security.rs",
            "src-tauri/src/servo_engine/security.rs"
        ]
        
        for file_path in security_files:
            if (self.project_root / file_path).exists():
                self.log(f"✅ Found {file_path}")
            else:
                self.log(f"⚠️ Missing {file_path}", "WARN")
        
        return True
    
    def generate_report(self):
        """Generate test report"""
        self.log("📊 Generating test report...")
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": self.test_results,
            "summary": {
                "total": len(self.test_results),
                "passed": sum(1 for r in self.test_results if r["passed"]),
                "failed": sum(1 for r in self.test_results if not r["passed"])
            }
        }
        
        # Save report
        with open("test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        self.log(f"✅ Test report saved to test_report.json")
        return report
    
    def run_all_tests(self):
        """Run all tests"""
        self.log("🚀 Starting Memori Browser Test Suite")
        self.log("=" * 50)
        
        tests = [
            ("Rust Environment", self.test_rust_environment),
            ("Node.js Environment", self.test_node_environment),
            ("Dependencies", self.test_dependencies),
            ("Rust Compilation", self.test_rust_compilation),
            ("Tauri Build", self.test_tauri_build),
            ("Browser Features", self.test_browser_features),
            ("Performance Features", self.test_performance_features),
            ("Security Features", self.test_security_features),
        ]
        
        for test_name, test_func in tests:
            self.log(f"\n🧪 Running test: {test_name}")
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                self.test_results.append({
                    "name": test_name,
                    "passed": result,
                    "duration": duration
                })
                
                if result:
                    self.log(f"✅ {test_name} PASSED ({duration:.2f}s)")
                else:
                    self.log(f"❌ {test_name} FAILED ({duration:.2f}s)")
                    
            except Exception as e:
                self.log(f"💥 {test_name} CRASHED: {e}", "ERROR")
                self.test_results.append({
                    "name": test_name,
                    "passed": False,
                    "error": str(e)
                })
        
        # Generate final report
        self.log("\n" + "=" * 50)
        report = self.generate_report()
        
        self.log(f"🎯 Test Summary:")
        self.log(f"   Total tests: {report['summary']['total']}")
        self.log(f"   Passed: {report['summary']['passed']}")
        self.log(f"   Failed: {report['summary']['failed']}")
        
        if report['summary']['failed'] == 0:
            self.log("🎉 All tests passed! Memori Browser is ready!")
            return True
        else:
            self.log("⚠️ Some tests failed. Check the logs above for details.")
            return False

def main():
    """Main test function"""
    tester = MemoriBrowserTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 Ready to launch Memori Browser!")
        print("Run: npm run tauri dev")
    else:
        print("\n🔧 Please fix the issues above before launching.")
        sys.exit(1)

if __name__ == "__main__":
    main()
