use std::collections::HashMap;
use std::sync::Arc;
use anyhow::Result;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tauri::{Manager, AppHandle, Window};
// use wry::WebViewBuilder; // Not needed for Tauri v1
use uuid::Uuid;

pub mod webview;
// pub mod process; // Temporarily disabled due to encoding issues
pub mod security;
pub mod performance;

use crate::browser::Tab;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub user_agent: String,
    pub enable_devtools: bool,
    pub enable_context_menu: bool,
    pub enable_file_drop: bool,
    pub enable_clipboard: bool,
    pub memory_limit_mb: u64,
    pub process_isolation: bool,
    pub hardware_acceleration: bool,
    pub javascript_enabled: bool,
    pub webgl_enabled: bool,
    pub media_autoplay: bool,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            user_agent: "Memori/1.0 (AI-Enhanced Browser)".to_string(),
            enable_devtools: cfg!(debug_assertions),
            enable_context_menu: true,
            enable_file_drop: true,
            enable_clipboard: true,
            memory_limit_mb: 512,
            process_isolation: true,
            hardware_acceleration: true,
            javascript_enabled: true,
            webgl_enabled: true,
            media_autoplay: false,
        }
    }
}

#[derive(Debug)]
pub struct BrowserEngine {
    app_handle: Option<AppHandle>,
    config: EngineConfig,
    webviews: Arc<RwLock<HashMap<String, WebviewHandle>>>,
    // process_manager: Arc<process::ProcessManager>, // Temporarily disabled
    security_manager: Arc<security::SecurityManager>,
    performance_monitor: Arc<performance::PerformanceMonitor>,
}

impl Default for BrowserEngine {
    fn default() -> Self {
        // Create a placeholder that will be properly initialized later
        Self {
            app_handle: None,
            config: EngineConfig::default(),
            webviews: Arc::new(RwLock::new(HashMap::new())),
            // process_manager: Arc::new(process::ProcessManager::default()), // Temporarily disabled
            security_manager: Arc::new(security::SecurityManager::default()),
            performance_monitor: Arc::new(performance::PerformanceMonitor::default()),
        }
    }
}

#[derive(Debug, Clone)]
pub struct WebviewHandle {
    pub tab_id: String,
    pub window_label: String,
    pub url: String,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
    pub zoom_level: f64,
    pub is_muted: bool,
    pub is_playing_audio: bool,
    pub memory_usage: u64,
    pub cpu_usage: f32,
}

impl BrowserEngine {
    pub async fn new(app_handle: AppHandle, config: EngineConfig) -> Result<Self> {
        // let process_manager = Arc::new(process::ProcessManager::new().await?); // Temporarily disabled
        let security_manager = Arc::new(security::SecurityManager::new(&config).await?);
        let performance_monitor = Arc::new(performance::PerformanceMonitor::new().await?);

        Ok(Self {
            app_handle: Some(app_handle),
            config,
            webviews: Arc::new(RwLock::new(HashMap::new())),
            // process_manager, // Temporarily disabled
            security_manager,
            performance_monitor,
        })
    }

    pub async fn create_webview(&self, tab: &Tab) -> Result<WebviewHandle> {
        let window_label = format!("webview-{}", tab.id);
        
        // Security check
        let sanitized_url = self.security_manager.sanitize_url(&tab.url).await?;
        
        // Create webview window
        let webview_window = WebviewWindowBuilder::new(
            &self.app_handle,
            &window_label,
            WebviewUrl::External(sanitized_url.parse()?)
        )
        .title(&tab.title)
        .inner_size(1200.0, 800.0)
        .min_inner_size(400.0, 300.0)
        .resizable(true)
        .maximizable(true)
        .minimizable(true)
        .closable(true)
        .focused(true)
        .visible(false) // Start hidden, show when ready
        .user_agent(&self.config.user_agent)
        .initialization_script(&self.get_initialization_script())
        .build()?;

        // Configure webview
        let webview = webview_window.webview();
        
        // Set up event handlers
        self.setup_webview_handlers(&webview_window, &tab.id).await?;

        let handle = WebviewHandle {
            tab_id: tab.id.clone(),
            window_label: window_label.clone(),
            url: sanitized_url,
            is_loading: true,
            can_go_back: false,
            can_go_forward: false,
            zoom_level: 1.0,
            is_muted: false,
            is_playing_audio: false,
            memory_usage: 0,
            cpu_usage: 0.0,
        };

        // Store webview handle
        self.webviews.write().insert(tab.id.clone(), handle.clone());

        // Start performance monitoring
        self.performance_monitor.start_monitoring(&tab.id).await?;

        tracing::info!("Created webview for tab: {}", tab.id);
        Ok(handle)
    }

    pub async fn navigate(&self, tab_id: &str, url: &str) -> Result<()> {
        let sanitized_url = self.security_manager.sanitize_url(url).await?;
        
        if let Some(mut handle) = self.webviews.write().get_mut(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    // Note: Tauri v1 doesn't have direct navigate, we'll use evaluate_script
                    let script = format!("window.location.href = '{}'", sanitized_url);
                    window.eval(&script)?;
                    handle.url = sanitized_url;
                    handle.is_loading = true;

                    tracing::info!("Navigating tab {} to: {}", tab_id, url);
                }
            }
        }
        
        Ok(())
    }

    pub async fn reload(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.read().get(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    window.eval("window.location.reload()")?;
                    tracing::info!("Reloaded tab: {}", tab_id);
                }
            }
        }
        Ok(())
    }

    pub async fn go_back(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.read().get(tab_id) {
            if handle.can_go_back {
                if let Some(app_handle) = &self.app_handle {
                    if let Some(window) = app_handle.get_window(&handle.window_label) {
                        window.eval("window.history.back()")?;
                        tracing::info!("Going back in tab: {}", tab_id);
                    }
                }
            }
        }
        Ok(())
    }

    pub async fn go_forward(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.read().get(tab_id) {
            if handle.can_go_forward {
                if let Some(app_handle) = &self.app_handle {
                    if let Some(window) = app_handle.get_window(&handle.window_label) {
                        window.eval("window.history.forward()")?;
                        tracing::info!("Going forward in tab: {}", tab_id);
                    }
                }
            }
        }
        Ok(())
    }

    pub async fn set_zoom(&self, tab_id: &str, zoom_level: f64) -> Result<()> {
        if let Some(mut handle) = self.webviews.write().get_mut(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    let script = format!("document.body.style.zoom = '{}'", zoom_level);
                    window.eval(&script)?;
                    handle.zoom_level = zoom_level;
                    tracing::info!("Set zoom level {} for tab: {}", zoom_level, tab_id);
                }
            }
        }
        Ok(())
    }

    pub async fn mute_tab(&self, tab_id: &str, muted: bool) -> Result<()> {
        if let Some(mut handle) = self.webviews.write().get_mut(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                let script = if muted {
                    r#"
                    document.querySelectorAll('audio, video').forEach(el => {
                        el.muted = true;
                        el.dataset.memoriMuted = 'true';
                    });
                    "#
                } else {
                    r#"
                    document.querySelectorAll('audio, video').forEach(el => {
                        if (el.dataset.memoriMuted === 'true') {
                            el.muted = false;
                            delete el.dataset.memoriMuted;
                        }
                    });
                    "#
                };
                
                    window.eval(script)?;
                    handle.is_muted = muted;
                    tracing::info!("Tab {} muted: {}", tab_id, muted);
                }
            }
        }
        Ok(())
    }

    pub async fn close_webview(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.write().remove(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    window.close()?;
                }
            }
            
            // Stop performance monitoring
            self.performance_monitor.stop_monitoring(tab_id).await?;
            
            tracing::info!("Closed webview for tab: {}", tab_id);
        }
        Ok(())
    }

    pub async fn show_webview(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.read().get(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    window.show()?;
                    window.set_focus()?;
                    tracing::info!("Showed webview for tab: {}", tab_id);
                }
            }
        }
        Ok(())
    }

    pub async fn hide_webview(&self, tab_id: &str) -> Result<()> {
        if let Some(handle) = self.webviews.read().get(tab_id) {
            if let Some(app_handle) = &self.app_handle {
                if let Some(window) = app_handle.get_window(&handle.window_label) {
                    window.hide()?;
                    tracing::info!("Hid webview for tab: {}", tab_id);
                }
            }
        }
        Ok(())
    }

    pub fn get_webview_handle(&self, tab_id: &str) -> Option<WebviewHandle> {
        self.webviews.read().get(tab_id).cloned()
    }

    pub fn get_all_webview_handles(&self) -> Vec<WebviewHandle> {
        self.webviews.read().values().cloned().collect()
    }

    fn get_initialization_script(&self) -> String {
        r#"
        // Memori Browser Initialization Script
        (function() {
            'use strict';
            
            // Audio detection
            let audioElements = [];
            let isPlayingAudio = false;
            
            function detectAudioPlayback() {
                const mediaElements = document.querySelectorAll('audio, video');
                let hasAudio = false;
                
                mediaElements.forEach(el => {
                    if (!el.paused && !el.muted && el.currentTime > 0) {
                        hasAudio = true;
                    }
                });
                
                if (hasAudio !== isPlayingAudio) {
                    isPlayingAudio = hasAudio;
                    window.__TAURI__.invoke('update_audio_state', {
                        tabId: window.__MEMORI_TAB_ID__,
                        isPlaying: hasAudio
                    });
                }
            }
            
            // Monitor audio every 500ms
            setInterval(detectAudioPlayback, 500);
            
            // Navigation state tracking
            let canGoBack = false;
            let canGoForward = false;
            
            function updateNavigationState() {
                const newCanGoBack = window.history.length > 1;
                const newCanGoForward = false; // Browser API limitation
                
                if (newCanGoBack !== canGoBack || newCanGoForward !== canGoForward) {
                    canGoBack = newCanGoBack;
                    canGoForward = newCanGoForward;
                    
                    window.__TAURI__.invoke('update_navigation_state', {
                        tabId: window.__MEMORI_TAB_ID__,
                        canGoBack,
                        canGoForward
                    });
                }
            }
            
            // Page load events
            window.addEventListener('load', () => {
                updateNavigationState();
                window.__TAURI__.invoke('page_loaded', {
                    tabId: window.__MEMORI_TAB_ID__,
                    url: window.location.href,
                    title: document.title
                });
            });
            
            // Title changes
            const titleObserver = new MutationObserver(() => {
                window.__TAURI__.invoke('title_changed', {
                    tabId: window.__MEMORI_TAB_ID__,
                    title: document.title
                });
            });
            
            titleObserver.observe(document.querySelector('title') || document.head, {
                childList: true,
                subtree: true
            });
            
            // Performance monitoring
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = {
                        tabId: window.__MEMORI_TAB_ID__,
                        loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
                        memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
                    };
                    
                    window.__TAURI__.invoke('performance_data', perfData);
                }, 1000);
            });
            
            console.log('Memori Browser initialized');
        })();
        "#.to_string()
    }

    async fn setup_webview_handlers(&self, window: &Window, tab_id: &str) -> Result<()> {
        let tab_id = tab_id.to_string();
        
        // Inject tab ID for JavaScript communication
        let script = format!("window.__MEMORI_TAB_ID__ = '{}';", tab_id);
        window.webview().evaluate_script(&script)?;
        
        Ok(())
    }
}
