use std::collections::HashMap;
use std::sync::Arc;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use anyhow::Result;
use uuid::Uuid;
use crate::database::DatabaseManager;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Tab {
    pub id: String,
    pub title: String,
    pub url: String,
    pub favicon: Option<String>,
    pub is_active: bool,
    pub is_loading: bool,
    pub is_playing_audio: bool,
    pub last_accessed: i64,
    pub bookmark_suggested: bool,
    pub visit_count: u32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Tab {
    pub fn new(url: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            title: "New Tab".to_string(),
            url,
            favicon: None,
            is_active: false,
            is_loading: true,
            is_playing_audio: false,
            last_accessed: now.timestamp_millis(),
            bookmark_suggested: false,
            visit_count: 1,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update_access_time(&mut self) {
        self.last_accessed = Utc::now().timestamp_millis();
        self.updated_at = Utc::now();
    }

    pub fn increment_visit_count(&mut self) {
        self.visit_count += 1;
        self.updated_at = Utc::now();
    }
}

pub struct BrowserManager {
    tabs: HashMap<String, Tab>,
    active_tab_id: Option<String>,
    database: Arc<DatabaseManager>,
    tab_history: Vec<String>, // For smart tab switching
}

impl BrowserManager {
    pub async fn new(database: Arc<DatabaseManager>) -> Result<Self> {
        let mut manager = Self {
            tabs: HashMap::new(),
            active_tab_id: None,
            database,
            tab_history: Vec::new(),
        };

        // Load existing tabs from database
        manager.load_tabs_from_database().await?;
        
        Ok(manager)
    }

    async fn load_tabs_from_database(&mut self) -> Result<()> {
        // Load persisted tabs from database
        let tabs = self.database.get_all_tabs().await?;
        
        for tab in tabs {
            self.tabs.insert(tab.id.clone(), tab);
        }

        // Set the most recently accessed tab as active
        if let Some(tab) = self.tabs.values().max_by_key(|t| t.last_accessed) {
            self.active_tab_id = Some(tab.id.clone());
        }

        Ok(())
    }

    pub async fn create_tab(&mut self, mut tab: Tab) -> Result<()> {
        // Deactivate all other tabs
        for existing_tab in self.tabs.values_mut() {
            existing_tab.is_active = false;
        }

        // Set new tab as active
        tab.is_active = true;
        tab.update_access_time();

        // Add to history
        self.tab_history.push(tab.id.clone());
        if self.tab_history.len() > 50 {
            self.tab_history.remove(0);
        }

        // Store in database
        self.database.save_tab(&tab).await?;

        // Update state
        self.active_tab_id = Some(tab.id.clone());
        self.tabs.insert(tab.id.clone(), tab);

        log::info!("Created new tab: {}", self.active_tab_id.as_ref().unwrap());
        Ok(())
    }

    pub async fn close_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.remove(tab_id) {
            // Remove from database
            self.database.delete_tab(tab_id).await?;

            // Remove from history
            self.tab_history.retain(|id| id != tab_id);

            // If this was the active tab, switch to the most recent one
            if self.active_tab_id.as_ref() == Some(&tab.id) {
                self.active_tab_id = self.get_next_active_tab();
                
                if let Some(new_active_id) = &self.active_tab_id {
                    if let Some(new_active_tab) = self.tabs.get_mut(new_active_id) {
                        new_active_tab.is_active = true;
                        new_active_tab.update_access_time();
                        self.database.save_tab(new_active_tab).await?;
                    }
                }
            }

            log::info!("Closed tab: {}", tab_id);
        }

        Ok(())
    }

    fn get_next_active_tab(&self) -> Option<String> {
        // Try to get the most recently accessed tab from history
        for tab_id in self.tab_history.iter().rev() {
            if self.tabs.contains_key(tab_id) {
                return Some(tab_id.clone());
            }
        }

        // Fallback to any available tab
        self.tabs.keys().next().cloned()
    }

    pub async fn switch_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            // Deactivate all tabs
            for existing_tab in self.tabs.values_mut() {
                existing_tab.is_active = false;
            }

            // Activate the selected tab
            tab.is_active = true;
            tab.update_access_time();

            // Update history
            self.tab_history.retain(|id| id != tab_id);
            self.tab_history.push(tab_id.to_string());

            // Update state
            self.active_tab_id = Some(tab_id.to_string());

            // Save to database
            self.database.save_tab(tab).await?;

            log::info!("Switched to tab: {}", tab_id);
        }

        Ok(())
    }

    pub async fn navigate_to_url(&mut self, tab_id: &str, url: &str) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.url = url.to_string();
            tab.is_loading = true;
            tab.increment_visit_count();
            tab.update_access_time();

            // Update title based on URL (simplified)
            tab.title = self.extract_title_from_url(url);

            // Save to database
            self.database.save_tab(tab).await?;

            // TODO: Trigger actual navigation in webview
            log::info!("Navigating tab {} to: {}", tab_id, url);
        }

        Ok(())
    }

    fn extract_title_from_url(&self, url: &str) -> String {
        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(domain) = parsed_url.domain() {
                return domain.to_string();
            }
        }
        url.to_string()
    }

    pub async fn get_all_tabs(&self) -> Vec<Tab> {
        self.tabs.values().cloned().collect()
    }

    pub async fn optimize_memory(&mut self) -> Result<()> {
        // Suspend inactive tabs that haven't been accessed recently
        let threshold = Utc::now().timestamp_millis() - (30 * 60 * 1000); // 30 minutes

        for tab in self.tabs.values_mut() {
            if !tab.is_active && tab.last_accessed < threshold {
                // TODO: Suspend tab's webview to save memory
                log::info!("Suspending inactive tab: {}", tab.id);
            }
        }

        Ok(())
    }

    pub fn get_active_tab(&self) -> Option<&Tab> {
        if let Some(active_id) = &self.active_tab_id {
            self.tabs.get(active_id)
        } else {
            None
        }
    }

    pub fn get_tab(&self, tab_id: &str) -> Option<&Tab> {
        self.tabs.get(tab_id)
    }

    pub fn get_tab_mut(&mut self, tab_id: &str) -> Option<&mut Tab> {
        self.tabs.get_mut(tab_id)
    }

    pub fn get_audio_playing_tabs(&self) -> Vec<String> {
        self.tabs
            .values()
            .filter(|tab| tab.is_playing_audio)
            .map(|tab| tab.id.clone())
            .collect()
    }

    pub async fn set_tab_audio_state(&mut self, tab_id: &str, is_playing: bool) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.is_playing_audio = is_playing;
            tab.updated_at = Utc::now();
            self.database.save_tab(tab).await?;
        }
        Ok(())
    }
}
