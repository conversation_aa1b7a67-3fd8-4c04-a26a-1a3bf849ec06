use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use url::Url;
use regex::Regex;
use tracing::{info, warn, error};
use crate::engine::EngineConfig;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityLevel {
    Strict,
    Balanced,
    Permissive,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityPolicy {
    pub level: SecurityLevel,
    pub block_malicious_urls: bool,
    pub block_tracking: bool,
    pub block_ads: bool,
    pub enable_https_only: bool,
    pub enable_csp: bool,
    pub enable_cors_protection: bool,
    pub allowed_protocols: HashSet<String>,
    pub blocked_domains: HashSet<String>,
    pub trusted_domains: HashSet<String>,
}

impl Default for SecurityPolicy {
    fn default() -> Self {
        let mut allowed_protocols = HashSet::new();
        allowed_protocols.insert("https".to_string());
        allowed_protocols.insert("http".to_string());
        allowed_protocols.insert("file".to_string());
        allowed_protocols.insert("data".to_string());

        Self {
            level: SecurityLevel::Balanced,
            block_malicious_urls: true,
            block_tracking: true,
            block_ads: false,
            enable_https_only: false,
            enable_csp: true,
            enable_cors_protection: true,
            allowed_protocols,
            blocked_domains: HashSet::new(),
            trusted_domains: HashSet::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityThreat {
    pub threat_type: ThreatType,
    pub url: String,
    pub description: String,
    pub severity: ThreatSeverity,
    pub timestamp: i64,
    pub blocked: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatType {
    Malware,
    Phishing,
    Tracking,
    Advertisement,
    MixedContent,
    UnsafeProtocol,
    SuspiciousDomain,
    DataExfiltration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatSeverity {
    Low,
    Medium,
    High,
    Critical,
}

pub struct SecurityManager {
    policy: Arc<RwLock<SecurityPolicy>>,
    threat_database: Arc<RwLock<HashMap<String, ThreatType>>>,
    tracking_patterns: Vec<Regex>,
    ad_patterns: Vec<Regex>,
    malicious_patterns: Vec<Regex>,
    phishing_indicators: Vec<Regex>,
    security_log: Arc<RwLock<Vec<SecurityThreat>>>,
}

impl SecurityManager {
    pub async fn new(config: &EngineConfig) -> Result<Self> {
        let policy = SecurityPolicy::default();
        
        let tracking_patterns = Self::compile_tracking_patterns()?;
        let ad_patterns = Self::compile_ad_patterns()?;
        let malicious_patterns = Self::compile_malicious_patterns()?;
        let phishing_indicators = Self::compile_phishing_patterns()?;

        let mut manager = Self {
            policy: Arc::new(RwLock::new(policy)),
            threat_database: Arc::new(RwLock::new(HashMap::new())),
            tracking_patterns,
            ad_patterns,
            malicious_patterns,
            phishing_indicators,
            security_log: Arc::new(RwLock::new(Vec::new())),
        };

        // Load threat database
        manager.load_threat_database().await?;
        
        info!("Security manager initialized");
        Ok(manager)
    }

    pub async fn sanitize_url(&self, url: &str) -> Result<String> {
        let parsed_url = Url::parse(url)
            .map_err(|e| anyhow!("Invalid URL: {}", e))?;

        // Check protocol
        if !self.is_protocol_allowed(parsed_url.scheme()) {
            return Err(anyhow!("Protocol not allowed: {}", parsed_url.scheme()));
        }

        // Check for security threats
        if let Some(threat) = self.check_url_threats(&parsed_url).await {
            self.log_security_threat(threat.clone()).await;
            
            match threat.severity {
                ThreatSeverity::Critical | ThreatSeverity::High => {
                    return Err(anyhow!("Blocked dangerous URL: {}", threat.description));
                }
                _ => {
                    warn!("Security warning for URL {}: {}", url, threat.description);
                }
            }
        }

        // Apply HTTPS upgrade if enabled
        let final_url = if self.should_upgrade_to_https(&parsed_url) {
            let mut upgraded = parsed_url.clone();
            upgraded.set_scheme("https").map_err(|_| anyhow!("Failed to upgrade to HTTPS"))?;
            upgraded.to_string()
        } else {
            parsed_url.to_string()
        };

        Ok(final_url)
    }

    pub async fn check_content_security(&self, content: &str, url: &str) -> Result<Vec<SecurityThreat>> {
        let mut threats = Vec::new();

        // Check for malicious scripts
        if self.contains_malicious_script(content) {
            threats.push(SecurityThreat {
                threat_type: ThreatType::Malware,
                url: url.to_string(),
                description: "Potentially malicious script detected".to_string(),
                severity: ThreatSeverity::High,
                timestamp: chrono::Utc::now().timestamp_millis(),
                blocked: true,
            });
        }

        // Check for data exfiltration attempts
        if self.contains_data_exfiltration(content) {
            threats.push(SecurityThreat {
                threat_type: ThreatType::DataExfiltration,
                url: url.to_string(),
                description: "Potential data exfiltration detected".to_string(),
                severity: ThreatSeverity::Medium,
                timestamp: chrono::Utc::now().timestamp_millis(),
                blocked: false,
            });
        }

        // Log all threats
        for threat in &threats {
            self.log_security_threat(threat.clone()).await;
        }

        Ok(threats)
    }

    pub fn generate_csp_header(&self, url: &str) -> String {
        let policy = self.policy.read();
        
        match policy.level {
            SecurityLevel::Strict => {
                "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' https:; object-src 'none'; media-src 'self' https:; frame-src 'none';"
            }
            SecurityLevel::Balanced => {
                "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; connect-src 'self' https: wss:; font-src 'self' https:; object-src 'self'; media-src 'self' https:; frame-src 'self' https:;"
            }
            SecurityLevel::Permissive => {
                "default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data:; connect-src *; font-src *; object-src *; media-src *; frame-src *;"
            }
        }.to_string()
    }

    pub async fn block_request(&self, url: &str) -> bool {
        let policy = self.policy.read();
        
        // Check blocked domains
        if let Ok(parsed_url) = Url::parse(url) {
            if let Some(domain) = parsed_url.domain() {
                if policy.blocked_domains.contains(domain) {
                    return true;
                }
            }
        }

        // Check tracking patterns
        if policy.block_tracking && self.is_tracking_url(url) {
            return true;
        }

        // Check ad patterns
        if policy.block_ads && self.is_ad_url(url) {
            return true;
        }

        // Check threat database
        if policy.block_malicious_urls {
            if let Some(threat_type) = self.threat_database.read().get(url) {
                match threat_type {
                    ThreatType::Malware | ThreatType::Phishing => return true,
                    _ => {}
                }
            }
        }

        false
    }

    async fn check_url_threats(&self, url: &Url) -> Option<SecurityThreat> {
        let url_str = url.as_str();
        
        // Check threat database
        if let Some(threat_type) = self.threat_database.read().get(url_str) {
            return Some(SecurityThreat {
                threat_type: threat_type.clone(),
                url: url_str.to_string(),
                description: format!("Known threat: {:?}", threat_type),
                severity: match threat_type {
                    ThreatType::Malware | ThreatType::Phishing => ThreatSeverity::Critical,
                    ThreatType::Tracking => ThreatSeverity::Low,
                    ThreatType::Advertisement => ThreatSeverity::Low,
                    _ => ThreatSeverity::Medium,
                },
                timestamp: chrono::Utc::now().timestamp_millis(),
                blocked: true,
            });
        }

        // Check phishing indicators
        if self.is_phishing_url(url) {
            return Some(SecurityThreat {
                threat_type: ThreatType::Phishing,
                url: url_str.to_string(),
                description: "Potential phishing site detected".to_string(),
                severity: ThreatSeverity::High,
                timestamp: chrono::Utc::now().timestamp_millis(),
                blocked: true,
            });
        }

        // Check for suspicious domains
        if self.is_suspicious_domain(url) {
            return Some(SecurityThreat {
                threat_type: ThreatType::SuspiciousDomain,
                url: url_str.to_string(),
                description: "Suspicious domain characteristics".to_string(),
                severity: ThreatSeverity::Medium,
                timestamp: chrono::Utc::now().timestamp_millis(),
                blocked: false,
            });
        }

        None
    }

    fn is_protocol_allowed(&self, protocol: &str) -> bool {
        self.policy.read().allowed_protocols.contains(protocol)
    }

    fn should_upgrade_to_https(&self, url: &Url) -> bool {
        self.policy.read().enable_https_only && url.scheme() == "http"
    }

    fn is_tracking_url(&self, url: &str) -> bool {
        self.tracking_patterns.iter().any(|pattern| pattern.is_match(url))
    }

    fn is_ad_url(&self, url: &str) -> bool {
        self.ad_patterns.iter().any(|pattern| pattern.is_match(url))
    }

    fn is_phishing_url(&self, url: &Url) -> bool {
        let url_str = url.as_str();
        self.phishing_indicators.iter().any(|pattern| pattern.is_match(url_str))
    }

    fn is_suspicious_domain(&self, url: &Url) -> bool {
        if let Some(domain) = url.domain() {
            // Check for suspicious characteristics
            let suspicious_patterns = [
                r"[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}", // IP addresses
                r".*\.tk$|.*\.ml$|.*\.ga$|.*\.cf$", // Suspicious TLDs
                r".*[0-9]{4,}.*", // Many numbers in domain
                r".*-{2,}.*", // Multiple consecutive hyphens
            ];

            for pattern in &suspicious_patterns {
                if let Ok(regex) = Regex::new(pattern) {
                    if regex.is_match(domain) {
                        return true;
                    }
                }
            }
        }
        false
    }

    fn contains_malicious_script(&self, content: &str) -> bool {
        self.malicious_patterns.iter().any(|pattern| pattern.is_match(content))
    }

    fn contains_data_exfiltration(&self, content: &str) -> bool {
        let exfiltration_patterns = [
            r"document\.cookie",
            r"localStorage\.getItem",
            r"sessionStorage\.getItem",
            r"navigator\.userAgent",
            r"screen\.width|screen\.height",
            r"new\s+XMLHttpRequest\(\)",
            r"fetch\s*\(",
        ];

        for pattern in &exfiltration_patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if regex.is_match(content) {
                    return true;
                }
            }
        }
        false
    }

    async fn load_threat_database(&mut self) -> Result<()> {
        // In a real implementation, this would load from external threat feeds
        // For now, we'll add some common malicious domains
        let mut db = self.threat_database.write();
        
        // Example malicious domains (in real implementation, load from threat feeds)
        db.insert("malware-example.com".to_string(), ThreatType::Malware);
        db.insert("phishing-example.com".to_string(), ThreatType::Phishing);
        
        info!("Loaded {} threat entries", db.len());
        Ok(())
    }

    async fn log_security_threat(&self, threat: SecurityThreat) {
        let mut log = self.security_log.write();
        log.push(threat.clone());
        
        // Keep only last 1000 entries
        if log.len() > 1000 {
            log.drain(0..500);
        }

        match threat.severity {
            ThreatSeverity::Critical => error!("CRITICAL SECURITY THREAT: {}", threat.description),
            ThreatSeverity::High => error!("HIGH SECURITY THREAT: {}", threat.description),
            ThreatSeverity::Medium => warn!("MEDIUM SECURITY THREAT: {}", threat.description),
            ThreatSeverity::Low => info!("LOW SECURITY THREAT: {}", threat.description),
        }
    }

    fn compile_tracking_patterns() -> Result<Vec<Regex>> {
        let patterns = [
            r"google-analytics\.com",
            r"googletagmanager\.com",
            r"facebook\.com/tr",
            r"doubleclick\.net",
            r"googlesyndication\.com",
            r"amazon-adsystem\.com",
            r"adsystem\.amazon",
            r"analytics\.twitter\.com",
            r"connect\.facebook\.net",
            r"scorecardresearch\.com",
        ];

        patterns.iter()
            .map(|p| Regex::new(p).map_err(|e| anyhow!("Invalid regex: {}", e)))
            .collect()
    }

    fn compile_ad_patterns() -> Result<Vec<Regex>> {
        let patterns = [
            r"ads\.",
            r"\.ads\.",
            r"advertising\.",
            r"adsystem\.",
            r"adnxs\.com",
            r"adsystem\.amazon",
            r"googlesyndication\.com",
            r"amazon-adsystem\.com",
        ];

        patterns.iter()
            .map(|p| Regex::new(p).map_err(|e| anyhow!("Invalid regex: {}", e)))
            .collect()
    }

    fn compile_malicious_patterns() -> Result<Vec<Regex>> {
        let patterns = [
            r"eval\s*\(",
            r"document\.write\s*\(",
            r"innerHTML\s*=",
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"vbscript:",
            r"onload\s*=",
            r"onerror\s*=",
        ];

        patterns.iter()
            .map(|p| Regex::new(p).map_err(|e| anyhow!("Invalid regex: {}", e)))
            .collect()
    }

    fn compile_phishing_patterns() -> Result<Vec<Regex>> {
        let patterns = [
            r"paypal.*\.com\..*",
            r"amazon.*\.com\..*",
            r"google.*\.com\..*",
            r"microsoft.*\.com\..*",
            r"apple.*\.com\..*",
            r".*-paypal\..*",
            r".*-amazon\..*",
            r".*-google\..*",
        ];

        patterns.iter()
            .map(|p| Regex::new(p).map_err(|e| anyhow!("Invalid regex: {}", e)))
            .collect()
    }

    pub async fn get_security_report(&self) -> SecurityReport {
        let log = self.security_log.read();
        let policy = self.policy.read();

        let total_threats = log.len();
        let blocked_threats = log.iter().filter(|t| t.blocked).count();
        let critical_threats = log.iter().filter(|t| matches!(t.severity, ThreatSeverity::Critical)).count();

        SecurityReport {
            total_threats,
            blocked_threats,
            critical_threats,
            security_level: policy.level.clone(),
            last_24h_threats: log.iter()
                .filter(|t| chrono::Utc::now().timestamp_millis() - t.timestamp < 24 * 60 * 60 * 1000)
                .count(),
        }
    }
}

#[derive(Debug, Serialize)]
pub struct SecurityReport {
    pub total_threats: usize,
    pub blocked_threats: usize,
    pub critical_threats: usize,
    pub security_level: SecurityLevel,
    pub last_24h_threats: usize,
}
