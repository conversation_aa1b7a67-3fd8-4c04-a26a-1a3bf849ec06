{"rustc": 1842507548689473721, "features": "[\"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 7668817577240348489, "path": 112649233362061231, "deps": [[3150220818285335163, "url", false, 18341895287846504686], [4381063397040571828, "webview2_com", false, 15263397648579214487], [4405182208873388884, "http", false, 8840872042809186653], [7653476968652377684, "windows", false, 2992580185220689273], [8008191657135824715, "thiserror", false, 4180399243737814734], [8292277814562636972, "tauri_utils", false, 11233730906379427994], [8319709847752024821, "uuid", false, 1289308709912327355], [8866577183823226611, "http_range", false, 4435597808393185585], [9689903380558560274, "serde", false, 3408844655873488430], [11693073011723388840, "raw_window_handle", false, 13820284972097331685], [13208667028893622512, "rand", false, 15282804574486708512], [14162324460024849578, "build_script_build", false, 7231285122285301195], [15367738274754116744, "serde_json", false, 1374970658716955935]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-83b2fdbbdd16d625\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}