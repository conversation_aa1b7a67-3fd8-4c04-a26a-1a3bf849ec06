{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 6157695917084337112, "deps": [[1017461770342116999, "sharded_slab", false, 17953645938090810558], [1359731229228270592, "thread_local", false, 10374109062860704895], [3424551429995674438, "tracing_core", false, 10544368577322630802], [3666196340704888985, "smallvec", false, 7779636784353838792], [8614575489689151157, "nu_ansi_term", false, 14157624912472532519], [10806489435541507125, "tracing_log", false, 16469770491541741968]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-1581cf13793ae259\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}