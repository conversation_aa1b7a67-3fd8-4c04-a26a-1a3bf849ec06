{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 10956804577811572050, "deps": [[4381063397040571828, "webview2_com", false, 1620107529343145568], [7653476968652377684, "windows", false, 2992580185220689273], [8292277814562636972, "tauri_utils", false, 10236367114593544923], [8319709847752024821, "uuid", false, 3205901600216734004], [8391357152270261188, "wry", false, 4886729707049260501], [11693073011723388840, "raw_window_handle", false, 13820284972097331685], [13208667028893622512, "rand", false, 15282804574486708512], [14162324460024849578, "tauri_runtime", false, 8873809557699011989], [16228250612241359704, "build_script_build", false, 1384881318742936544]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-119974f2866bdbb7\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}