{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"default\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"rustfft\"]", "target": 18060829941030025544, "profile": 2241668132362809309, "path": 16699527116776230800, "deps": [[5986029879202738730, "log", false, 8783125871897346445], [6511429716036861196, "bytemuck", false, 8532258671526913108], [10435729446543529114, "bitflags", false, 876963220237312145], [13847662864258534762, "arrayvec", false, 14874022161764569744], [17917672826516349275, "lazy_static", false, 2390581858057747413]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\symphonia-core-37c6273b940ffcb3\\dep-lib-symphonia_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}