{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 112649233362061231, "deps": [[3150220818285335163, "url", false, 12462817959554494608], [4381063397040571828, "webview2_com", false, 9628900648478487160], [4405182208873388884, "http", false, 5424496942209081958], [7653476968652377684, "windows", false, 3715085020098981444], [8008191657135824715, "thiserror", false, 13263018949999206050], [8292277814562636972, "tauri_utils", false, 1597570392376008931], [8319709847752024821, "uuid", false, 12411518605472132979], [8866577183823226611, "http_range", false, 11421844695356099708], [9689903380558560274, "serde", false, 14936975360657098552], [11693073011723388840, "raw_window_handle", false, 2214521491364938754], [13208667028893622512, "rand", false, 14866442077337734985], [14162324460024849578, "build_script_build", false, 9116805984013380334], [15367738274754116744, "serde_json", false, 967918925515909091]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-a04bf193e06f2ea7\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}