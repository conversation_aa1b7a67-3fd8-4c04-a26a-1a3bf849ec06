[package]
name = "memori-browser"
version = "0.1.0"
description = "AI-Enhanced Browser"
authors = ["Memori Team"]
license = "MIT"
edition = "2021"

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[dependencies]
# Core Tauri
tauri = { version = "1.0", features = ["clipboard-read-text", "clipboard-write-text", "dialog-message", "dialog-open", "dialog-save", "fs-create-dir", "fs-read-dir", "fs-read-file", "fs-write-file", "global-shortcut-all", "http-request", "notification-all", "os-all", "path-all", "shell-execute", "shell-open", "window-center", "window-close", "window-create", "window-hide", "window-maximize", "window-minimize", "window-set-focus", "window-set-fullscreen", "window-set-position", "window-set-resizable", "window-set-size", "window-set-title", "window-show", "window-unmaximize", "window-unminimize"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async Runtime
tokio = { version = "1.0", features = ["full"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }

# Time & IDs
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# Error Handling
anyhow = "1.0"
thiserror = "1.0"

# Search & Text Processing
fuzzy-matcher = "0.3"

# URL & Network
url = "2.5"
regex = "1.10"
reqwest = { version = "0.11", features = ["json"] }

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Concurrency
parking_lot = "0.12"
dashmap = "5.0"
once_cell = "1.19"

# Audio Detection
rodio = { version = "0.17", optional = true }

# Compression
lz4_flex = "0.11"

# URL encoding
urlencoding = "2.1"

# Random number generation
rand = "0.8"

# System Information
sysinfo = "0.30"
dirs = "5.0"
num_cpus = "1.16"
log = "0.4"

# Window Management
wry = "0.24"
winit = "0.28"

# Graphics (for Servo integration)
# webrender = { version = "0.62", optional = true }
# webrender_api = { version = "0.62", optional = true }
# surfman = { version = "0.9", optional = true }

# Servo Engine (when available)
# servo = { git = "https://github.com/servo/servo", optional = true }

[features]
default = ["custom-protocol", "audio"]
custom-protocol = ["tauri/custom-protocol"]
audio = ["rodio"]
servo = []