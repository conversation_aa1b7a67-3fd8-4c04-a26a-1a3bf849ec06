[package]
name = "memori-browser"
version = "0.1.0"
description = "AI-Enhanced Browser with Memory Intelligence"
authors = ["Memori Team"]
license = "MIT"
repository = "https://github.com/memori/memori-browser"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
thiserror = "1.0"
fuzzy-matcher = "0.3"
url = "2.5"
regex = "1.10"
reqwest = { version = "0.12", features = ["json"] }
tao = "0.30"
wry = "0.47"
log = "0.4"
env_logger = "0.11"
dirs = "5.0"
parking_lot = "0.12"
dashmap = "6.0"
once_cell = "1.19"
rayon = "1.10"

# Audio detection dependencies
rodio = "0.19"
cpal = "0.15"

# AI/ML dependencies for intelligent features
candle-core = "0.8"
candle-nn = "0.8"
candle-transformers = "0.8"
tokenizers = "0.20"

# Voice recognition
speech-recognition = "0.2"
whisper-rs = "0.2"

# Performance monitoring
sysinfo = "0.32"
memory-stats = "1.2"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true

[profile.dev]
incremental = true
debug = true
