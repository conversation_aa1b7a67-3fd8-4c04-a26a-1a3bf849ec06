[package]
name = "memori-browser"
version = "0.1.0"
description = "AI-Enhanced Browser with Memory Intelligence"
authors = ["Memori Team"]
license = "MIT"
repository = "https://github.com/memori/memori-browser"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Core Tauri with minimal features (Servo handles rendering)
tauri = { version = "2.0", features = [
    "protocol-asset",
    "window-all",
    "system-tray",
    "notification-all",
    "global-shortcut-all",
    "clipboard-all",
    "fs-all",
    "shell-open",
    "process-all"
] }
tauri-plugin-shell = "2.0"
tauri-plugin-window-state = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-notification = "2.0"
tauri-plugin-global-shortcut = "2.0"
tauri-plugin-clipboard-manager = "2.0"

# Servo Browser Engine (Latest)
servo = { git = "https://github.com/servo/servo", features = [
    "webgl",
    "webgpu",
    "media-gstreamer",
    "canvas2d-azure",
    "canvas2d-raqote"
] }
servo-media = { git = "https://github.com/servo/media" }
servo-media-gstreamer = { git = "https://github.com/servo/media", optional = true }

# Servo Core Components
webrender = "0.62"
webrender_api = "0.62"
euclid = "0.22"
gleam = "0.15"
surfman = "0.9"
sparkle = "0.1"

# Servo Script Engine
script = { git = "https://github.com/servo/servo", package = "script" }
script_traits = { git = "https://github.com/servo/servo", package = "script_traits" }
js = { git = "https://github.com/servo/servo", package = "js" }

# Servo Layout Engine
layout_2020 = { git = "https://github.com/servo/servo", package = "layout_2020" }
style = { git = "https://github.com/servo/servo", package = "style" }
selectors = "0.25"
cssparser = "0.31"

# Servo Networking
net = { git = "https://github.com/servo/servo", package = "net" }
net_traits = { git = "https://github.com/servo/servo", package = "net_traits" }
hyper = "1.0"
hyper-rustls = "0.27"

# Serialization & Core
serde = { version = "1.0", features = ["derive", "rc"] }
serde_json = "1.0"
bincode = "1.3"

# Async Runtime (optimized)
tokio = { version = "1.0", features = [
    "rt-multi-thread",
    "macros",
    "sync",
    "time",
    "fs",
    "net",
    "process",
    "signal"
] }

# Database (optimized for performance)
sqlx = { version = "0.8", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono",
    "uuid",
    "migrate",
    "json"
] }
rusqlite = { version = "0.32", features = ["bundled", "chrono", "serde_json"] }

# Time & IDs
chrono = { version = "0.4", features = ["serde", "clock"] }
uuid = { version = "1.0", features = ["v4", "serde", "fast-rng"] }

# Error Handling
anyhow = "1.0"
thiserror = "1.0"

# Search & Text Processing
fuzzy-matcher = "0.3"
tantivy = "0.22"  # Full-text search engine
aho-corasick = "1.1"  # Fast string matching

# URL & Network
url = "2.5"
regex = "1.10"
reqwest = { version = "0.12", features = ["json", "stream", "gzip", "brotli"] }
hyper = { version = "1.0", features = ["full"] }
hickory-dns = "0.24"  # Fast DNS resolution

# Browser Engine Core
wry = { version = "0.47", features = ["devtools", "protocol", "tray"] }
tao = { version = "0.30", features = ["serde"] }
webkit2gtk = { version = "2.0", optional = true }  # Linux
webview2-com = { version = "0.33", optional = true }  # Windows

# Logging & Monitoring
tracing = { version = "0.1", features = ["log"] }
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
sysinfo = "0.32"
memory-stats = "1.2"

# Concurrency & Performance
parking_lot = "0.12"
dashmap = "6.0"
once_cell = "1.19"
rayon = "1.10"
crossbeam = "0.8"
flume = "0.11"  # Fast MPSC channels

# Audio Processing (optimized)
cpal = { version = "0.15", features = ["jack"] }
rodio = { version = "0.19", features = ["symphonia-all"] }
symphonia = { version = "0.5", features = ["all"] }

# AI/ML (lightweight alternatives)
ort = { version = "2.0", features = ["cuda", "tensorrt"] }  # ONNX Runtime
candle-core = { version = "0.8", optional = true }
tokenizers = { version = "0.20", optional = true }

# Voice Recognition (platform-specific)
whisper-rs = { version = "0.2", optional = true }
speech-dispatcher = { version = "0.16", optional = true }  # Linux TTS

# Compression & Encoding
lz4_flex = "0.11"  # Fast compression
zstd = "0.13"
base64 = "0.22"

# Platform-specific optimizations
[target.'cfg(windows)'.dependencies]
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_System_Threading",
    "Win32_System_Memory",
    "Win32_Graphics_Gdi",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Media_Audio",
    "Win32_System_Com"
] }
webview2-com = "0.33"

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.26"
objc = "0.2"
core-foundation = "0.10"
core-graphics = "0.24"

[target.'cfg(target_os = "linux")'.dependencies]
webkit2gtk = "2.0"
gtk = "0.18"
gdk = "0.18"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true

[profile.dev]
incremental = true
debug = true
