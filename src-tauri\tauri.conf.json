{"productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "identifier": "com.memori.browser", "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true, "execute": true, "sidecar": true, "scope": [{"name": "bin/ai-engine", "sidecar": true, "args": true}]}, "window": {"all": true, "create": true, "center": true, "requestUserAttention": true, "setResizable": true, "setTitle": true, "maximize": true, "unmaximize": true, "minimize": true, "unminimize": true, "show": true, "hide": true, "close": true, "setDecorations": true, "setAlwaysOnTop": true, "setSize": true, "setMinSize": true, "setMaxSize": true, "setPosition": true, "setFullscreen": true, "setFocus": true, "setIcon": true, "setSkipTaskbar": true, "setCursorGrab": true, "setCursorVisible": true, "setCursorIcon": true, "setCursorPosition": true, "startDragging": true, "print": true}, "fs": {"all": true, "readFile": true, "writeFile": true, "readDir": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true, "scope": ["$APPDATA", "$APPDATA/**", "$RESOURCE/**", "$APP/**", "$LOG/**", "$TEMP/**"]}, "path": {"all": true}, "os": {"all": true}, "http": {"all": true, "request": true, "scope": ["https://**", "http://**"]}, "notification": {"all": true}, "dialog": {"all": true, "open": true, "save": true, "message": true, "ask": true, "confirm": true}, "clipboard": {"all": true, "writeText": true, "readText": true}, "globalShortcut": {"all": true}, "protocol": {"all": true, "asset": true, "assetScope": ["**"]}, "process": {"all": true, "exit": true, "relaunch": true, "relaunchDangerousAllowSymlinkMaladaptation": false}}, "bundle": {"active": true, "targets": "all", "identifier": "com.memori.browser", "icon": ["icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "© 2025 <PERSON><PERSON><PERSON>", "category": "DeveloperTool", "shortDescription": "Advanced AI-powered web browser", "longDescription": "Memori Browser is a next-generation web browser with built-in AI assistance, intelligent bookmarking, performance monitoring, and advanced security features."}, "security": {"csp": null, "devCsp": null, "freezePrototype": false, "dangerousDisableAssetCspModification": {"script": true, "style": true}, "dangerousRemoteDomainIpcAccess": [{"domain": "*", "windows": ["main"], "enableTauriAPI": true}]}, "updater": {"active": false}, "windows": [{"title": "<PERSON><PERSON><PERSON>", "width": 1400, "height": 900, "resizable": true, "fullscreen": false, "alwaysOnTop": false, "transparent": false, "decorations": true, "skipTaskbar": false, "fileDropEnabled": true, "center": true, "minWidth": 800, "minHeight": 600, "maxWidth": null, "maxHeight": null, "x": null, "y": null, "focus": true, "url": "index.html", "label": "main", "titleBarStyle": "Overlay", "hiddenTitle": false, "acceptFirstMouse": false, "tabbingIdentifier": null, "additionalBrowserArgs": "--enable-features=NetworkService,NetworkServiceInProcess --disable-features=VizDisplayCompositor"}], "cli": {"description": "<PERSON><PERSON><PERSON> Browser CLI", "longDescription": null, "beforeHelp": null, "afterHelp": null, "args": [], "subcommands": {}}, "systemTray": {"iconPath": "icons/icon.ico", "iconAsTemplate": true, "menuOnLeftClick": false}}}