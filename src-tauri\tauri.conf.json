{"build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devPath": "../dist", "distDir": "../dist"}, "package": {"productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0"}, "tauri": {"allowlist": {"all": true}, "bundle": {"active": true, "targets": "all", "identifier": "com.memori.browser"}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "<PERSON><PERSON><PERSON>", "width": 1200, "height": 800}]}}