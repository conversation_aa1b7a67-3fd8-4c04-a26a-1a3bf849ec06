﻿{\ build\:{\beforeDevCommand\:\npm run dev\,\beforeBuildCommand\:\npm run build\,\devPath\:\http://localhost:1420\,\distDir\:\../dist\,\withGlobalTauri\:false},\package\:{\productName\:\Memori Browser\,\version\:\1.0.0\},\tauri\:{\allowlist\:{\all\:false,\shell\:{\all\:false,\open\:true}},\bundle\:{\active\:true,\targets\:\all\,\identifier\:\com.memori.browser\,\icon\:[\icons/32x32.png\,\icons/128x128.png\,\icons/<EMAIL>\,\icons/icon.icns\,\icons/icon.ico\]},\security\:{\csp\:null},\windows\:[{\fullscreen\:false,\resizable\:true,\title\:\Memori Browser\,\width\:1200,\height\:800}]}}
