{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devPath": "../dist", "distDir": "../dist", "withGlobalTauri": true}, "package": {"productName": "memori-browser", "version": "0.1.0"}, "tauri": {"allowlist": {"all": true, "shell": {"all": false, "open": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.memori.browser", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "resizable": true, "title": "<PERSON><PERSON><PERSON>", "width": 1200, "height": 800}]}}