{"rustc": 1842507548689473721, "features": "[\"default\", \"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 14528530421717216679, "deps": [[3007252114546291461, "tao", false, 10657866926312953817], [3150220818285335163, "url", false, 12462817959554494608], [3540822385484940109, "windows_implement", false, 10822027573035950352], [3722963349756955755, "once_cell", false, 6551543648015782790], [4381063397040571828, "webview2_com", false, 9628900648478487160], [4405182208873388884, "http", false, 5424496942209081958], [4684437522915235464, "libc", false, 13600667349365002734], [5986029879202738730, "log", false, 6447583467671528173], [7653476968652377684, "windows", false, 3715085020098981444], [8008191657135824715, "thiserror", false, 13263018949999206050], [8391357152270261188, "build_script_build", false, 5449838677121257818], [9689903380558560274, "serde", false, 14936975360657098552], [11989259058781683633, "dunce", false, 13399713777212210225], [15367738274754116744, "serde_json", false, 967918925515909091]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-328ebaaa8647ec35\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}