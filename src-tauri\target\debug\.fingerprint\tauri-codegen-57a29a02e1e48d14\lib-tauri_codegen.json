{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 18439556900360439042, "deps": [[3060637413840920116, "proc_macro2", false, 14143828410907889523], [4899080583175475170, "semver", false, 11305946926429843468], [7392050791754369441, "ico", false, 7629597027086246491], [8008191657135824715, "thiserror", false, 17112181259498819850], [8292277814562636972, "tauri_utils", false, 10876553706513495807], [8319709847752024821, "uuid", false, 8146834730660952770], [9451456094439810778, "regex", false, 2655336738413197755], [9689903380558560274, "serde", false, 16672505391114472160], [9857275760291862238, "sha2", false, 769525638080044329], [10301936376833819828, "json_patch", false, 2606004407255906207], [12687914511023397207, "png", false, 15722801764774915128], [14132538657330703225, "brotli", false, 9025679280435260004], [15367738274754116744, "serde_json", false, 7155776428369128707], [15622660310229662834, "walkdir", false, 15099281372303424341], [17990358020177143287, "quote", false, 9791753232960825671], [18066890886671768183, "base64", false, 856503484629315922]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-57a29a02e1e48d14\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}