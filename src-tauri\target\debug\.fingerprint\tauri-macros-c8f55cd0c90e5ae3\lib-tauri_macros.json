{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11774737094483147028, "deps": [[2713742371683562785, "syn", false, 14621964450831803193], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [8292277814562636972, "tauri_utils", false, 13786297685237440777], [13077543566650298139, "heck", false, 11003689655865491247], [17492769205600034078, "tauri_codegen", false, 4582071554583522922], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-c8f55cd0c90e5ae3\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}