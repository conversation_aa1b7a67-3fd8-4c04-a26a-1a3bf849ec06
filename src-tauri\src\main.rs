// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{Manager, State, AppHandle, WebviewWindow};
use tokio::sync::RwLock;
use tracing::{info, error, warn};
use anyhow::Result;

// Simplified modules for testing
mod servo_commands;

// Mock structures for testing
pub struct BrowserManager;
pub struct MemoryManager;
pub struct SearchEngine;
pub struct AIAssistant;
pub struct AudioDetector;
pub struct DatabaseManager;
pub struct IntelligenceEngine;
pub struct BrowserEngine;
pub struct EngineConfig;
pub struct Tab;

// Simplified Application State for testing
#[derive(Clone)]
pub struct AppState {
    pub browser_manager: Arc<tokio::sync::RwLock<BrowserManager>>,
    pub memory_manager: Arc<tokio::sync::RwLock<MemoryManager>>,
}

// Error types for better error handling
#[derive(Debug, thiserror::Error)]
pub enum BrowserError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Tauri error: {0}")]
    Tauri(#[from] tauri::Error),
    #[error("Engine error: {0}")]
    Engine(String),
    #[error("Security error: {0}")]
    Security(String),
    #[error("Performance error: {0}")]
    Performance(String),
}

type BrowserResult<T> = Result<T, BrowserError>;

// Tauri Commands
#[tauri::command]
async fn create_tab(
    state: State<'_, AppState>,
    tab: Tab,
) -> Result<(), String> {
    let mut browser = state.browser_manager.write().await;
    browser.create_tab(tab).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn close_tab(
    state: State<'_, AppState>,
    tab_id: String,
) -> Result<(), String> {
    let mut browser = state.browser_manager.write().await;
    browser.close_tab(&tab_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn switch_tab(
    state: State<'_, AppState>,
    tab_id: String,
) -> Result<(), String> {
    let mut browser = state.browser_manager.write().await;
    browser.switch_tab(&tab_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_all_tabs(
    state: State<'_, AppState>,
) -> Result<Vec<Tab>, String> {
    let browser = state.browser_manager.read().await;
    Ok(browser.get_all_tabs().await)
}

#[tauri::command]
async fn navigate_to_url(
    state: State<'_, AppState>,
    tab_id: String,
    url: String,
) -> Result<(), String> {
    let mut browser = state.browser_manager.write().await;
    browser.navigate_to_url(&tab_id, &url).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_url_completions(
    state: State<'_, AppState>,
    input: String,
) -> Result<Vec<intelligence::UrlCompletion>, String> {
    let intelligence = state.intelligence.read().await;
    intelligence.get_url_completions(&input).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn start_voice_recognition(
    state: State<'_, AppState>,
) -> Result<String, String> {
    let ai = state.ai_assistant.read().await;
    ai.start_voice_recognition().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn auto_bookmark(
    state: State<'_, AppState>,
    url: String,
    title: String,
    visit_count: u32,
) -> Result<(), String> {
    let intelligence = state.intelligence.read().await;
    intelligence.auto_bookmark(&url, &title, visit_count).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn send_chat_message(
    state: State<'_, AppState>,
    message: String,
) -> Result<String, String> {
    let ai = state.ai_assistant.read().await;
    ai.process_chat_message(&message).await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn optimize_memory(
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut browser = state.browser_manager.write().await;
    browser.optimize_memory().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn preload_frequent_sites(
    state: State<'_, AppState>,
) -> Result<(), String> {
    let intelligence = state.intelligence.read().await;
    intelligence.preload_frequent_sites().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn detect_audio_tabs(
    state: State<'_, AppState>,
) -> Result<Vec<String>, String> {
    let audio_detector = state.audio_detector.read().await;
    Ok(audio_detector.get_audio_playing_tabs().await)
}

#[tauri::command]
async fn search_tabs(
    state: State<'_, AppState>,
    query: String,
) -> Result<Vec<Tab>, String> {
    let search = state.search_engine.read().await;
    let browser = state.browser_manager.read().await;
    let tabs = browser.get_all_tabs().await;
    Ok(search.search_tabs(&tabs, &query).await)
}

#[tauri::command]
async fn get_memory_suggestions(
    state: State<'_, AppState>,
    context: String,
) -> Result<Vec<memory::MemoryItem>, String> {
    let memory = state.memory_manager.read().await;
    memory.get_suggestions(&context).await.map_err(|e| e.to_string())
}

#[tokio::main]
async fn main() {
    env_logger::init();
    
    // Initialize database
    let database = Arc::new(
        DatabaseManager::new().await
            .expect("Failed to initialize database")
    );

    // Initialize application state
    let app_state = AppState {
        browser_manager: Arc::new(RwLock::new(
            BrowserManager::new(database.clone()).await
                .expect("Failed to initialize browser manager")
        )),
        memory_manager: Arc::new(RwLock::new(
            MemoryManager::new(database.clone()).await
                .expect("Failed to initialize memory manager")
        )),
        search_engine: Arc::new(RwLock::new(
            SearchEngine::new().await
                .expect("Failed to initialize search engine")
        )),
        ai_assistant: Arc::new(RwLock::new(
            AIAssistant::new().await
                .expect("Failed to initialize AI assistant")
        )),
        audio_detector: Arc::new(RwLock::new(
            AudioDetector::new().await
                .expect("Failed to initialize audio detector")
        )),
        database: database.clone(),
        intelligence: Arc::new(RwLock::new(
            IntelligenceEngine::new(database.clone()).await
                .expect("Failed to initialize intelligence engine")
        )),
    };

    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            create_tab,
            close_tab,
            switch_tab,
            get_all_tabs,
            navigate_to_url,
            get_url_completions,
            start_voice_recognition,
            auto_bookmark,
            send_chat_message,
            optimize_memory,
            preload_frequent_sites,
            detect_audio_tabs,
            search_tabs,
            get_memory_suggestions,
            // Servo commands for testing
            servo_commands::servo_create_tab,
            servo_commands::servo_get_config,
            servo_commands::servo_get_performance_metrics,
        ])
        .setup(|app| {
            // Setup window
            let window = app.get_webview_window("main").unwrap();
            
            // Set window properties for optimal performance
            window.set_title("Memori Browser - AI Enhanced Browsing").unwrap();
            
            // Start background services
            let app_handle = app.handle().clone();
            tokio::spawn(async move {
                // Start audio monitoring
                if let Some(state) = app_handle.try_state::<AppState>() {
                    let mut audio_detector = state.audio_detector.write().await;
                    if let Err(e) = audio_detector.start_monitoring().await {
                        log::error!("Failed to start audio monitoring: {}", e);
                    }
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
