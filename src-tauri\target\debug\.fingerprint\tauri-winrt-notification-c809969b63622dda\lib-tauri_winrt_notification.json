{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 16434929074262014398, "profile": 2241668132362809309, "path": 18043488662803801270, "deps": [[3334271191048661305, "windows_version", false, 8008773991765628102], [8002575747386574528, "quick_xml", false, 1949573143562155271], [11882924694914494186, "windows", false, 3340165087622513998]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-c809969b63622dda\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}