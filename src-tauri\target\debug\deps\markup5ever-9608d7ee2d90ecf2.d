C:\Users\<USER>\memori-browser\src-tauri\target\debug\deps\markup5ever-9608d7ee2d90ecf2.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/generated.rs C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/named_entities.rs

C:\Users\<USER>\memori-browser\src-tauri\target\debug\deps\libmarkup5ever-9608d7ee2d90ecf2.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/generated.rs C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs:
C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/generated.rs:
C:\Users\<USER>\memori-browser\src-tauri\target\debug\build\markup5ever-58f239e120750e92\out/named_entities.rs:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\memori-browser\\src-tauri\\target\\debug\\build\\markup5ever-58f239e120750e92\\out
