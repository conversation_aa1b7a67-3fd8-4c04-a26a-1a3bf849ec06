{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 16403927197798629762, "deps": [[561782849581144631, "html5ever", false, 12123142773592453394], [3150220818285335163, "url", false, 18341895287846504686], [3334271191048661305, "windows_version", false, 8008773991765628102], [4071963112282141418, "serde_with", false, 13485753129696411154], [4899080583175475170, "semver", false, 11880096215548401512], [5986029879202738730, "log", false, 8783125871897346445], [6262254372177975231, "kuchiki", false, 5575356041990980331], [6606131838865521726, "ctor", false, 5074607611391229500], [6997837210367702832, "infer", false, 5502004201897835968], [8008191657135824715, "thiserror", false, 4180399243737814734], [9689903380558560274, "serde", false, 3408844655873488430], [10301936376833819828, "json_patch", false, 9416917399159784171], [11989259058781683633, "dunce", false, 15638666645565303671], [14132538657330703225, "brotli", false, 4677452714718885644], [15367738274754116744, "serde_json", false, 1374970658716955935], [15622660310229662834, "walkdir", false, 11814528668954357079], [15932120279885307830, "memchr", false, 3149709465132557017], [17155886227862585100, "glob", false, 14676570654132184308], [17186037756130803222, "phf", false, 14708771357521893772]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-2bf366a72a688729\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}